import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';

class OrderTypeLabel extends StatelessWidget {
  final int? typeId;

  const OrderTypeLabel({
    super.key,
    required this.typeId,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: AlignmentDirectional.topStart,
      child: Container(
        height: AppSize.height(31),
        width: AppSize.width(77),
        decoration: BoxDecoration(
          borderRadius: BorderRadiusDirectional.only(
            topStart: Radius.circular(10),
            bottomEnd: Radius.circular(10),
          ),
          color: AppColors.primaryWithOpacity3,
        ),
        child: Center(
          child: Text(
            typeId == 0 ? 'takeAway'.tr : 'dineIn'.tr,
            style: AppTextStyle.primary12800,
          ),
        ),
      ),
    );
  }
}
