// To parse this JSON data, do
//
//     final changeOrderStatus = changeOrderStatusFromJson(jsonString);

import 'dart:convert';

ChangeOrderStatusModel changeOrderStatusFromJson(String str) =>
    ChangeOrderStatusModel.fromJson(json.decode(str));

String changeOrderStatusToJson(ChangeOrderStatusModel data) =>
    json.encode(data.toJson());

class ChangeOrderStatusModel {
  final int? status;
  final String? result;
  final int? data;

  ChangeOrderStatusModel({
    this.status,
    this.result,
    this.data,
  });

  factory ChangeOrderStatusModel.fromJson(Map<String, dynamic> json) =>
      ChangeOrderStatusModel(
        status: json["status"],
        result: json["result"],
        data: json["data"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "result": result,
        "data": data,
      };
}
