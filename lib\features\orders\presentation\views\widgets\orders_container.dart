import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../getx/controllers/orders_controller.dart';
import 'orders_grid_view.dart';

class OrdersContainer extends GetView<OrdersController> {
  const OrdersContainer({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.lavenderGray,
          ),
          borderRadius: BorderRadius.circular(10),
        ),
        child: OrdersGridView(),
      ),
    );
  }
}
