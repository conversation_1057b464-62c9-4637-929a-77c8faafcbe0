apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'dev.flutter.flutter-gradle-plugin'

android {
    namespace = "com.example.point_of_sale"
    compileSdk = 31
    defaultConfig {
        targetSdk 34
        minSdkVersion flutter.minSdkVersion
    }
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.point_of_sale"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }
    lint {
        baseline = file("lint-baseline.xml")
        checkDependencies = true      // also check libraries
        checkAllWarnings = false      // only fatal by default
        abortOnError = true           // fail build on fatal issues
    }
    flavorDimensions += "default"

    productFlavors {
        production {
            dimension "default"
            resValue "string", "app_name", "Production Point of Sale"
        }
         staging {
            dimension "default"
            resValue "string", "app_name", "Staging Point of Sale"
        }
        development {
            dimension "default"
            applicationIdSuffix ".dev"
            resValue "string", "app_name", "Development Point of Sale"
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}
