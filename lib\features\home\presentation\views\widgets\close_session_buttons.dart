import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/routes/app_pages.dart';
import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/close_session_controller.dart';

class CloseSessionButtom extends GetView<CloseSessionController> {
  const CloseSessionButtom({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: AlignmentDirectional.bottomEnd,
      child: Row(
        children: [
          InkWell(
            onTap: () {
              controller.closeSession();
              Get.offAllNamed(Routes.createSession);
            },
            child: Container(
              height: AppSize.height(41),
              width: AppSize.width(210),
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  'closeSession'.tr,
                  style: AppTextStyle.white18800,
                ),
              ),
            ),
          ),
          Spacer(),
          InkWell(
            onTap: () {
              controller.closeSession();
              controller.logout();
            },
            child: Container(
              height: AppSize.height(41),
              width: AppSize.width(210),
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  'closeSessionAndLogout'.tr,
                  style: AppTextStyle.white18800,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
