import 'package:dartz/dartz.dart';

import '../../../../core/errors/failure.dart';
import '../../../../core/services/use_case.dart';
import '../../data/models/kitchen_orders_model.dart';
import '../entities/kitchen_orders_entity.dart';
import '../repo/kitchen_orders_repo.dart';

class KitchenOrdersUseCase
    extends UseCase<KitchenOrdersModel, KitchenOrdersEntity> {
  final KitchenOrdersRepo kitchenOrdersRepo;
  KitchenOrdersUseCase(this.kitchenOrdersRepo);
  @override
  Future<Either<Failure, KitchenOrdersModel>> call(KitchenOrdersEntity params) {
    params.loading(true);
    final result = kitchenOrdersRepo.getOrders(params);
    result.then((value) {
      params.loading(false);
    });
    return result;
  }
}
