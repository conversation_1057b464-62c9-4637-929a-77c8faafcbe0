import '../../../../core/services/use_case.dart';

class CreateNewOrderEntity extends Param {
  String tenantId,
      companyId,
      branchId,
      userId,
      tableId,
      typeId,
      customerName,
      customerId,
      posSessionId;
  CreateNewOrderEntity({
    required super.loading,
    required this.tenantId,
    required this.companyId,
    required this.branchId,
    required this.userId,
    required this.tableId,
    required this.typeId,
    required this.customerName,
    required this.customerId,
    required this.posSessionId,
  });
  Map<String, dynamic> toJson() {
    return {
      "tenant_id": tenantId,
      "company_id": companyId,
      "branch_id": branchId,
      "created_by": userId,
      "table_id": tableId,
      "type_id": typeId,
      "customer_name": customerName,
      "customer_id": customerId,
      "pos_sessions_id": posSessionId,
    };
  }
}
