import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/features/home/<USER>/views/widgets/choose_order_type_dialog.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/size_config.dart';
import '../../../../../core/utils/size_utils.dart';
import '../../getx/controllers/create_new_order_controller.dart';

class AddOrderButton extends GetView<CreateNewOrderController> {
  const AddOrderButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        controller.tablesActivationController.tablesActive.isTrue
            ? showDialog(
                barrierDismissible: false,
                context: context,
                builder: (BuildContext context) {
                  return const ChooseOrderTypeDialog();
                },
              )
            : controller.getCreateNewOrder();
      },
      child: Container(
        height: AppSize.height(29),
        width: AppSize.width(157),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          color: AppColors.green,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_circle_outline_outlined,
              color: AppColors.white,
              size: getSize(19),
            ),
            SizedBox(width: AppSize.width(4)),
            Text(
              'newOrder'.tr,
              style: AppTextStyle.white16800,
            ),
          ],
        ),
      ),
    );
  }
}
