import 'package:flutter/material.dart';
import 'package:point_of_sale/core/utils/size_utils.dart';

import 'app_colors.dart';

class AppTextStyle {
  static TextStyle primary16500 = TextStyle(
    fontSize: getFontSize(16),
    fontWeight: FontWeight.normal,
    color: AppColors.primaryColor,
    fontFamily: 'Cairo',
  );
  static TextStyle primary12700 = TextStyle(
    fontSize: getFontSize(12),
    fontWeight: FontWeight.w700,
    color: AppColors.primaryColor,
    fontFamily: 'Cairo',
  );
  static TextStyle primary11600 = TextStyle(
    fontSize: getFontSize(11),
    fontWeight: FontWeight.w600,
    color: AppColors.primaryColor,
    fontFamily: 'Cairo',
  );
  static TextStyle primary10700 = TextStyle(
    fontSize: getFontSize(10),
    fontWeight: FontWeight.w700,
    color: AppColors.primaryColor,
    fontFamily: 'Cairo',
  );
  static TextStyle primary12800 = TextStyle(
    fontSize: getFontSize(12),
    fontWeight: FontWeight.w800,
    color: AppColors.primaryColor,
    fontFamily: 'Cairo',
  );
  static TextStyle white14800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w800,
    color: AppColors.white,
  );
  static TextStyle white14700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w700,
    color: AppColors.white,
  );
  static TextStyle white14600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w600,
    color: AppColors.white,
  );
  static TextStyle white12600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(12),
    fontWeight: FontWeight.w600,
    color: AppColors.white,
  );
  static TextStyle white18800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(18),
    fontWeight: FontWeight.w800,
    color: AppColors.white,
  );
  static TextStyle white20600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.normal,
    color: AppColors.white,
  );
  static TextStyle white17600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(17),
    fontWeight: FontWeight.w600,
    color: AppColors.white,
  );
  static TextStyle white20700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.w700,
    color: AppColors.white,
  );
  static TextStyle primary18500 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(18),
    fontWeight: FontWeight.normal,
    color: AppColors.primaryColor,
  );
  static TextStyle primary30700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(30),
    fontWeight: FontWeight.w700,
    color: AppColors.primaryColor,
  );
  static TextStyle primary30600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(30),
    fontWeight: FontWeight.w600,
    color: AppColors.primaryColor,
  );
  static TextStyle white16500 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.normal,
    color: AppColors.white,
  );
  static TextStyle lavenderGray32600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(32),
    fontWeight: FontWeight.w600,
    color: AppColors.lavenderGray,
  );
  static TextStyle primary14500 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.normal,
    color: AppColors.primaryColor,
  );
  static TextStyle primary8800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(8),
    fontWeight: FontWeight.bold,
    color: AppColors.primaryColor,
  );
  static TextStyle primary10800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(10),
    fontWeight: FontWeight.bold,
    color: AppColors.primaryColor,
  );
  static TextStyle primary8700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(8),
    fontWeight: FontWeight.w700,
    color: AppColors.primaryColor,
  );
  static TextStyle lavndery2Gray14600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w600,
    color: AppColors.lavenderGray2,
  );
  static TextStyle primary14600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w600,
    color: AppColors.primaryColor,
  );
  static TextStyle primary16700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.w700,
    color: AppColors.primaryColor,
  );
  static TextStyle primary14700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w700,
    color: AppColors.primaryColor,
  );
  static TextStyle white12500 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(12),
    fontWeight: FontWeight.normal,
    color: AppColors.white,
  );
  static TextStyle primary10500 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(10),
    fontWeight: FontWeight.normal,
    color: AppColors.primaryColor,
  );
  static TextStyle white8500 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(8),
    fontWeight: FontWeight.normal,
    color: AppColors.white,
  );
  static TextStyle darkgreen16600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.w600,
    color: AppColors.darkGreen,
  );
  static TextStyle darkgreen14600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w600,
    color: AppColors.darkGreen,
  );
  static TextStyle darkgreen20600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.w600,
    color: AppColors.darkGreen,
  );
  static TextStyle darkgreen24800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(24),
    fontWeight: FontWeight.w800,
    color: AppColors.darkGreen,
  );
  static TextStyle darkgreen20800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.w800,
    color: AppColors.darkGreen,
  );
  static TextStyle primary16600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.w600,
    color: AppColors.primaryColor,
  );
  static TextStyle primary16800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.bold,
    color: AppColors.primaryColor,
  );
  static TextStyle white20800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.bold,
    color: AppColors.white,
  );
  static TextStyle white16700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.w700,
    color: AppColors.white,
  );
  static TextStyle white10600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(10),
    fontWeight: FontWeight.w600,
    color: AppColors.white,
  );
  static TextStyle white29600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(29),
    fontWeight: FontWeight.w600,
    color: AppColors.white,
  );
  static TextStyle white32700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(32),
    fontWeight: FontWeight.w700,
    color: AppColors.white,
  );
  static TextStyle white48800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(48),
    fontWeight: FontWeight.bold,
    color: AppColors.white,
  );
  static TextStyle primary18700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(18),
    fontWeight: FontWeight.w700,
    color: AppColors.primaryColor,
  );
  static TextStyle primary18800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(18),
    fontWeight: FontWeight.w800,
    color: AppColors.primaryColor,
  );
  static TextStyle primary18900 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(18),
    fontWeight: FontWeight.w900,
    color: AppColors.primaryColor,
  );
  static TextStyle primary18600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(18),
    fontWeight: FontWeight.w600,
    color: AppColors.primaryColor,
  );
  static TextStyle primary20600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.w600,
    color: AppColors.primaryColor,
  );
  static TextStyle primary20800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.bold,
    color: AppColors.primaryColor,
  );
  static TextStyle primary22800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(22),
    fontWeight: FontWeight.bold,
    color: AppColors.primaryColor,
  );
  static TextStyle primary24800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(24),
    fontWeight: FontWeight.w800,
    color: AppColors.primaryColor,
  );
  static TextStyle primary20700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.w700,
    color: AppColors.primaryColor,
  );
  static TextStyle primary20900 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.w900,
    color: AppColors.primaryColor,
  );
  static TextStyle primary53700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(53),
    fontWeight: FontWeight.w700,
    color: AppColors.primaryColor,
  );
  static TextStyle primary53800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(53),
    fontWeight: FontWeight.w800,
    color: AppColors.primaryColor,
  );
  static TextStyle primary28700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(28),
    fontWeight: FontWeight.w700,
    color: AppColors.primaryColor,
  );
  static TextStyle primary32800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(32),
    fontWeight: FontWeight.bold,
    color: AppColors.primaryColor,
  );
  static TextStyle primary22600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(22),
    fontWeight: FontWeight.w600,
    color: AppColors.primaryColor,
  );
  static TextStyle primary22700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(22),
    fontWeight: FontWeight.w700,
    color: AppColors.primaryColor,
  );
  static TextStyle green32800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(32),
    fontWeight: FontWeight.bold,
    color: AppColors.green,
  );
  static TextStyle green20600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.w600,
    color: AppColors.green,
  );
  static TextStyle green20700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.w700,
    color: AppColors.green,
  );
  static TextStyle green20800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(20),
    fontWeight: FontWeight.w800,
    color: AppColors.green,
  );
  static TextStyle green14800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w800,
    color: AppColors.green,
  );
  static TextStyle green14600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w600,
    color: AppColors.green,
  );
  static TextStyle green16600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.w600,
    color: AppColors.green,
  );
  static TextStyle green16700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.w700,
    color: AppColors.green,
  );
  static TextStyle grey14600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w600,
    color: AppColors.lavenderGray,
  );
  static TextStyle grey16600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.w600,
    color: AppColors.lavenderGray,
  );
  static TextStyle lavenderGray16600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.w600,
    color: AppColors.lavenderGray,
  );
  static TextStyle lavenderGray15400 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(15),
    fontWeight: FontWeight.w400,
    color: AppColors.lavenderGray,
  );
  static TextStyle grey314600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w600,
    color: AppColors.grey3,
  );
  static TextStyle lavenderGray11600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(11),
    fontWeight: FontWeight.w600,
    color: AppColors.lavenderGray,
  );
  static TextStyle lavenderGray14600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w600,
    color: AppColors.lavenderGray,
  );
  static TextStyle lavenderGray14800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w800,
    color: AppColors.lavenderGray,
  );
  static TextStyle primary43800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(43),
    fontWeight: FontWeight.bold,
    color: AppColors.primaryColor,
  );
  static TextStyle primary40800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(40),
    fontWeight: FontWeight.bold,
    color: AppColors.primaryColor,
  );
  static TextStyle primary48800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(48),
    fontWeight: FontWeight.bold,
    color: AppColors.primaryColor,
  );
  static TextStyle white16800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.bold,
    color: AppColors.white,
  );
  static TextStyle primary14800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.bold,
    color: AppColors.primaryColor,
  );
  static TextStyle primary15500 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(15),
    fontWeight: FontWeight.w500,
    color: AppColors.primaryColor,
  );
  static TextStyle white10800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(10),
    fontWeight: FontWeight.bold,
    color: AppColors.white,
  );
  static TextStyle white8800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(8),
    fontWeight: FontWeight.bold,
    color: AppColors.white,
  );
  static TextStyle white12800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(12),
    fontWeight: FontWeight.bold,
    color: AppColors.white,
  );
  static TextStyle red18700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(18),
    fontWeight: FontWeight.bold,
    color: AppColors.error,
  );
  static TextStyle red20800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(200),
    fontWeight: FontWeight.bold,
    color: AppColors.red,
  );
  static TextStyle red14700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(14),
    fontWeight: FontWeight.w700,
    color: AppColors.red,
  );
  static TextStyle red16700 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(16),
    fontWeight: FontWeight.w700,
    color: AppColors.red,
  );
  static TextStyle darkGray11600 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(11),
    fontWeight: FontWeight.w600,
    color: AppColors.darkGray,
  );
  static TextStyle darkGray12800 = TextStyle(
    fontFamily: 'Cairo',
    fontSize: getFontSize(12),
    fontWeight: FontWeight.w800,
    color: AppColors.darkGray,
  );
}
