import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/orders_controller.dart';
import 'orders_status_section.dart';

class OrdersTabs extends GetView<OrdersController> {
  const OrdersTabs({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsetsGeometry.symmetric(
        horizontal: AppSize.width(10),
      ),
      child: Row(
        children: [
          OrdersStatusSection(),
          Spacer(),
          Obx(
            () => InkWell(
              onTap: () {
                controller.changeOrderTypeIndex(0);
              },
              child: RichText(
                text: TextSpan(
                  text: 'all'.tr,
                  style: controller.orderTypeIndex.value == 0
                      ? AppTextStyle.green14800
                          .copyWith(decoration: TextDecoration.underline)
                      : AppTextStyle.primary14700,
                ),
              ),
            ),
          ),
          SizedBox(
            width: AppSize.width(12),
          ),
          Obx(
            () => InkWell(
              onTap: () {
                controller.changeOrderTypeIndex(1);
              },
              child: RichText(
                text: TextSpan(
                  text: 'takeAway'.tr,
                  style: controller.orderTypeIndex.value == 1
                      ? AppTextStyle.green14800
                          .copyWith(decoration: TextDecoration.underline)
                      : AppTextStyle.primary14700,
                ),
              ),
            ),
          ),
          SizedBox(
            width: AppSize.width(12),
          ),
          Obx(
            () => InkWell(
              onTap: () {
                controller.changeOrderTypeIndex(2);
              },
              child: RichText(
                text: TextSpan(
                  text: 'dineIn'.tr,
                  style: controller.orderTypeIndex.value == 2
                      ? AppTextStyle.green14800
                          .copyWith(decoration: TextDecoration.underline)
                      : AppTextStyle.primary14700,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
