import '../../../../core/errors/exceptions.dart';
import '../../../../core/services/api_service.dart';
import '../../../../core/utils/constants.dart';
import '../../domain/entitis/change_order_status_entity.dart';
import '../models/change_order_status_model.dart';

abstract class ChangeOrderStatusRemoteDataSource {
  Future<ChangeOrderStatusModel> changeOrderStatus(
      ChangeOrderStatusEntity changeOrderStatusEntity);
}

class ChangeOrderStatusRemoteDataSourceImpl
    implements ChangeOrderStatusRemoteDataSource {
  final ApiService apiService;

  ChangeOrderStatusRemoteDataSourceImpl(this.apiService);

  @override
  Future<ChangeOrderStatusModel> changeOrderStatus(
      ChangeOrderStatusEntity changeOrderStatusEntity) async {
    final result = await apiService.orderStatusPutRequest(
      '${Constants.baseUrl}sales/pos/orders/status/${changeOrderStatusEntity.tenantId}/${changeOrderStatusEntity.companyId}/${changeOrderStatusEntity.branchId}/${changeOrderStatusEntity.orderId}/${changeOrderStatusEntity.newStatusId}',
    );

    final responseData = result.data;
    if (result.statusCode == 200 || result.statusCode == 201) {
      return ChangeOrderStatusModel.fromJson(result.data);
    } else {
      throw AppExceptions(
        message: responseData['message'] ?? 'Authentication failed',
        statusCode: result.statusCode,
        data: responseData,
      );
    }
  }
}
