import 'package:get/get.dart';
import '../../../../../core/errors/exceptions.dart';
import '../../../../../core/errors/failure.dart';
import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/widgets/failed_snack_bar.dart';
import '../../../data/models/kitchen_orders_model.dart';
import '../../../domain/entities/kitchen_orders_entity.dart';
import '../../../domain/use_case/kitchen_orders_use_case.dart';

class KitchenOrdersController extends GetxController {
  final KitchenOrdersUseCase kitchenOrdersUseCase;
  final CashDataSource cashDataSource = Get.put(CashDataSource());
  final loading = true.obs;
  KitchenOrdersController(this.kitchenOrdersUseCase);
  KitchenOrdersModel kitchenOrdersModel = KitchenOrdersModel();
  Future<void> getKitchenOrders() async {
    final result = await kitchenOrdersUseCase(
      KitchenOrdersEntity(
        loading: loading,
        tenantId: cashDataSource.box.read('tenantId'),
        companyId: cashDataSource.box.read('companyId'),
        branchId: cashDataSource.box.read('branchId'),
        userId: cashDataSource.box.read('userId'),
        statusId: '2',
        sessionId: '0',
      ),
    );
    result.fold(
      (failure) {
        String errorMessage;
        if (failure is ServerFailure) {
          errorMessage = failure.message;
        } else if (failure is AppExceptions) {
          errorMessage = failure.message;
        } else {
          errorMessage = 'somethingWentWrongPleaseTryAgainLater'.tr;
        }

        failedSnaskBar(errorMessage);
      },
      (data) {
        kitchenOrdersModel = data;
      },
    );
  }

  @override
  void onInit() {
    super.onInit();
    getKitchenOrders();
  }
}
