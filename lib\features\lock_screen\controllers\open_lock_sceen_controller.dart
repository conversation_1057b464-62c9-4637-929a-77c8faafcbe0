import 'dart:async';

import 'package:get/get.dart';

import '../../../core/routes/app_pages.dart';

class OpenLockScreenController extends GetxController {
  // final LockScreenTimerController lockScreenTimerController =
  //     Get.find<LockScreenTimerController>();
  final RxInt timer = 15.obs;
  void countDown() {
    if (timer.value > 1) {
      timer.value--;
    }
  }

  void countUp() {
    if (timer.value < 30) {
      timer.value++;
    }
  }

  Duration get timeoutDuration => Duration(minutes: timer.value);

  Timer? _inactivityTimer;

  void initializeTimer() {
    _resetTimer();
  }

  void _resetTimer() {
    _inactivityTimer?.cancel();
    _inactivityTimer = Timer(timeoutDuration, _onTimeout);
  }

  void _onTimeout() {
    Get.toNamed(
      Routes.lockScreen,
    );
  }

  void userInteractionDetected() {
    _resetTimer();
  }

  @override
  void onClose() {
    _inactivityTimer?.cancel();
    super.onClose();
  }
}
