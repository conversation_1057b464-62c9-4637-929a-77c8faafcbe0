import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/kitchen_orders_controller.dart';
import 'notes_content.dart';
import 'notes_tab_bar.dart';

class OrderNotesSection extends GetView<KitchenOrdersController> {
  final int index;
  const OrderNotesSection({
    super.key,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    // Create individual PageController and selected index for each order
    final pageController = PageController(initialPage: 0);
    final selectedNotesTypeIndex = 0.obs;

    return Column(
      children: [
        Divider(
          color: AppColors.grey3,
          height: 0,
        ),
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: AppSize.width(10),
            vertical: AppSize.height(10),
          ),
          child: Align(
            alignment: AlignmentDirectional.topStart,
            child: Text(
              'notes'.tr,
              style: AppTextStyle.primary12800,
            ),
          ),
        ),
        NotesTabBar(
          selectedNotesTypeIndex: selectedNotesTypeIndex,
          pageController: pageController,
          index: index,
        ),
        SizedBox(
          height: AppSize.height(8),
        ),
        NotesContent(
          selectedNotesTypeIndex: selectedNotesTypeIndex,
          pageController: pageController,
          index: index,
        ),
      ],
    );
  }
}
