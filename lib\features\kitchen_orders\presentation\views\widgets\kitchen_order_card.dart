import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../../../orders/presentation/getx/controllers/change_order_status_controller.dart';
import '../../getx/controllers/kitchen_orders_controller.dart';
import 'order_actions_menu.dart';
import 'order_footer.dart';
import 'order_header.dart';
import 'order_notes_section.dart';
import 'order_status_badge.dart';
import 'order_type_label.dart';

class KitchenOrderCard extends GetView<KitchenOrdersController> {
  final int index;
  final double itemWidth;

  const KitchenOrderCard({
    super.key,
    required this.index,
    required this.itemWidth,
  });

  @override
  Widget build(BuildContext context) {
    final hasKitchenNotes =
        controller.kitchenOrdersModel.data![index].kitchenNotes != null &&
            controller.kitchenOrdersModel.data![index].kitchenNotes!.isNotEmpty;

    return Container(
      width: itemWidth,
      height: hasKitchenNotes ? AppSize.height(440) : AppSize.height(260),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.lavenderGray,
        ),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Stack(
        children: [
          OrderTypeLabel(
              typeId: controller.kitchenOrdersModel.data?[index].typeId),
          OrderActionsMenu(
              index: index,
              orderId:
                  controller.kitchenOrdersModel.data?[index].id?.toString()),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                OrderHeader(
                  orderNo: controller.kitchenOrdersModel.data?[index].orderNo,
                  index: index,
                ),
                OrderFooter(
                  customerName:
                      controller.kitchenOrdersModel.data?[index].customerName,
                  netAmount:
                      controller.kitchenOrdersModel.data?[index].netAmount,
                ),
                SizedBox(
                  height: AppSize.height(8),
                ),
                Spacer(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    InkWell(
                      onTap: () {
                        Get.find<ChangeOrderStatusController>()
                            .changeOrderStatus(
                          controller.kitchenOrdersModel.data?[index].id
                                  .toString() ??
                              '',
                          '3',
                        );
                        controller.getKitchenOrders();
                      },
                      child: Container(
                        height: AppSize.height(31),
                        width: AppSize.width(65),
                        margin: EdgeInsetsDirectional.only(
                          start: AppSize.width(10),
                          bottom: AppSize.height(10),
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppColors.lavenderGray,
                          ),
                          borderRadius: BorderRadiusDirectional.circular(6),
                        ),
                        child: Center(
                          child: Text(
                            'complete'.tr,
                            style: AppTextStyle.primary12800,
                          ),
                        ),
                      ),
                    ),
                    OrderStatusBadge(
                        statusId: controller
                            .kitchenOrdersModel.data?[index].statusId),
                  ],
                ),
                if (hasKitchenNotes) ...[
                  OrderNotesSection(index: index),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
