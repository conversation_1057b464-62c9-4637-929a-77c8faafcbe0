import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/orders/presentation/views/widgets/orders_container_item.dart';

import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../../../../core/widgets/loading_widget.dart';
import '../../getx/controllers/orders_controller.dart';

class OrdersGridView extends GetView<OrdersController> {
  const OrdersGridView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        if (controller.loading.isTrue) {
          return const Center(
            child: LoadingWidget(),
          );
        }

        // Always use filteredOrders as it contains the result of all applied filters
        if (controller.filteredOrders.isEmpty) {
          return Center(
            child: Text(
              (controller.searchQuery.value.isNotEmpty || controller.orderTypeIndex.value != 0)
                  ? 'noOrdersFound'.tr
                  : 'noOrders'.tr,
              style: AppTextStyle.primary18600,
            ),
          );
        }

        return GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            mainAxisSpacing: AppSize.height(24),
            crossAxisSpacing: AppSize.width(24),
            crossAxisCount: 4,
          ),
          padding: EdgeInsets.symmetric(
            vertical: AppSize.height(24),
            horizontal: AppSize.width(24),
          ),
          itemCount: controller.filteredOrders.length,
          itemBuilder: (BuildContext context, int index) {
            return OrdersContainerItem(
              index: index,
            );
          },
        );
      },
    );
  }
}
