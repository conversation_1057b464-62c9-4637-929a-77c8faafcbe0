import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';

class SettingsSwitcherItem extends StatelessWidget {
  const SettingsSwitcherItem({
    super.key,
    required this.height,
    required this.title,
    required this.switcherValue,
    required this.onChanged,
  });

  final double height;
  final String title;
  final bool switcherValue;
  final void Function(bool) onChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.width(16),
        vertical: AppSize.height(12),
      ),
      margin: EdgeInsets.symmetric(
        horizontal: AppSize.width(16),
        vertical: AppSize.height(12),
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border.all(
          color: AppColors.lavenderGray,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: AppTextStyle.primary16800,
            ),
          ),
          FlutterSwitch(
            width: AppSize.width(40),
            height: AppSize.height(20),
            toggleSize: AppSize.height(18),
            borderRadius: 10,
            padding: 2,
            value: switcherValue,
            activeColor: AppColors.green,
            inactiveColor: AppColors.grey3,
            toggleColor: AppColors.white,
            onToggle: onChanged,
            activeIcon: Icon(Icons.check, size: 16, color: AppColors.white),
            inactiveIcon: Icon(Icons.close, size: 16, color: AppColors.white),
          ),
        ],
      ),
    );
  }
}
