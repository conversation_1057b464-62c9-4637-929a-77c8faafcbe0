import '../../../../core/services/use_case.dart';

class CloseSessionEntity extends Param {
  String closeCash,
      closeNotes,
      totalCash,
      cashIn,
      cashOut,
      difference,
      openCash;
  int sessionId;

  CloseSessionEntity({
    required super.loading,
    required this.sessionId,
    required this.closeCash,
    required this.closeNotes,
    required this.totalCash,
    required this.cashIn,
    required this.cashOut,
    required this.difference,
    required this.openCash,
  });
  Map<String, dynamic> toJson() {
    return {
      'close_cash': closeCash,
      'close_notes': closeNotes,
      'total_cash': totalCash,
      'cash_in': cashIn,
      'cash_out': cashOut,
      'difference': difference,
      'open_cash': openCash,
    };
  }
}
