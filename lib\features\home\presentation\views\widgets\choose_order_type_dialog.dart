import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/home/<USER>/views/widgets/choose_order_type_dialog_body.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/create_new_order_controller.dart';

class ChooseOrderTypeDialog extends GetView<CreateNewOrderController> {
  const ChooseOrderTypeDialog({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(10),
        ),
      ),
      backgroundColor: AppColors.white,
      contentPadding: EdgeInsets.symmetric(
        vertical: AppSize.height(0),
        horizontal: AppSize.width(0),
      ),
      content: const ChooseOrderTypeDialogBody(),
    );
  }
}
