import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/orders/presentation/views/widgets/orders_container.dart';

import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/orders_controller.dart';
import 'orders_tabs.dart';

class OrdersViewBody extends GetView<OrdersController> {
  const OrdersViewBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.symmetric(
        horizontal: AppSize.width(47),
        vertical: AppSize.height(15),
      ),
      child: Column(
        children: const [
          OrdersTabs(),
          OrdersContainer(),
        ],
      ),
    );
  }
}
