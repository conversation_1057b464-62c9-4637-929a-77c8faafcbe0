import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/home/<USER>/views/widgets/close_cash_text_field.dart';
import 'package:point_of_sale/features/home/<USER>/views/widgets/close_notes_text_field.dart';

import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';

class ClosingSessionFields extends StatelessWidget {
  const ClosingSessionFields({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Align(
              alignment: AlignmentDirectional.topStart,
              child: Text(
                'cashCount'.tr,
                style: AppTextStyle.primary18800,
              ),
            ),
            SizedBox(
              height: AppSize.height(8),
            ),
            Sized<PERSON>ox(
              height: AppSize.height(41),
              width: AppSize.width(186),
              child: const CloseCashTextField(),
            ),
          ],
        ),
        <PERSON><PERSON><PERSON>ox(
          width: AppSize.width(12),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'note'.tr,
              style: AppTextStyle.primary18800,
            ),
            SizedBox(
              height: AppSize.height(8),
            ),
            SizedBox(
              height: AppSize.height(121),
              width: AppSize.width(467),
              child: const CloseNotesTextField(),
            ),
          ],
        ),
      ],
    );
  }
}
