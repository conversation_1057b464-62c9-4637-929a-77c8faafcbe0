import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../../../../core/widgets/confirm_and_cancel_buttons.dart';
import '../../../../../core/widgets/loading_widget.dart';
import '../../../../tables/presentation/views/widgets/tables_right_side.dart';
import '../../getx/controllers/create_new_order_controller.dart';

class ChooseTableDialog extends GetView<CreateNewOrderController> {
  const ChooseTableDialog({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => controller.tablesController.loading.isTrue
          ? const LoadingWidget()
          : AlertDialog(
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(10),
                ),
              ),
              // contentPadding: EdgeInsets.zero,
              title: Center(
                child: Text(
                  'tables'.tr,
                  style: AppTextStyle.primary20800,
                ),
              ),
              backgroundColor: AppColors.white,
              content: SizedBox(
                height: AppSize.height(672),
                width: AppSize.width(725),
                child: Column(
                  children: [
                    TablesRightSide(
                      margin: EdgeInsets.all(0),
                    ),
                    ConfirmAndCancelButtons(
                      cancelTap: () {
                        Get.back();
                      },
                      confirmTap: () {
                        Get.back();
                        controller.getCreateNewOrder();
                        controller.ordersListController.scrollController
                            .animateTo(
                          controller.ordersListController.scrollController
                              .position.maxScrollExtent,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
