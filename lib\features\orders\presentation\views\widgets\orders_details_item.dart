import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/orders_controller.dart';

class OrdersDetailsItem extends GetView<OrdersController> {
  const OrdersDetailsItem({
    super.key,
    required this.itemsIndex,
    required this.index,
  });

  final int itemsIndex, index;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          controller.getOrderAtIndex(index)?.items?[itemsIndex].productName ?? '',
          style: AppTextStyle.primary12800,
        ),
        Spacer(),
        Text(
          'X',
          style: AppTextStyle.primary12700,
        ),
        SizedBox(
          width: AppSize.width(3),
        ),
        Container(
          height: AppSize.height(12),
          width: AppSize.height(12),
          decoration: BoxDecoration(
            color: AppColors.third,
          ),
          child: Center(
            child: Text(
              controller.getOrderAtIndex(index)?.items?[itemsIndex].qty ?? '',
              style: AppTextStyle.primary10700,
            ),
          ),
        ),
      ],
    );
  }
}
