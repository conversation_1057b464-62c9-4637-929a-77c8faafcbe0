import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/kitchen_orders_controller.dart';

class NotesTabBar extends GetView<KitchenOrdersController> {
  final RxInt selectedNotesTypeIndex;
  final PageController pageController;
  final int index;
  const NotesTabBar({
    super.key,
    required this.selectedNotesTypeIndex,
    required this.pageController,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: AppSize.height(18),
      child: ListView.separated(
        padding: EdgeInsetsDirectional.only(
          start: AppSize.width(10),
        ),
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, notesIndex) {
          return InkWell(
            onTap: () {
              selectedNotesTypeIndex.value = notesIndex;
              pageController.animateToPage(
                notesIndex,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            },
            child: Obx(
              () => AnimatedContainer(
                duration: Duration(milliseconds: 200),
                height: AppSize.height(18),
                width: AppSize.width(50),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(3),
                  color: selectedNotesTypeIndex.value == notesIndex
                      ? AppColors.primaryColor
                      : AppColors.white,
                  border: Border.all(
                    color: AppColors.primaryColor,
                  ),
                ),
                child: Center(
                  child: Text(
                    controller.kitchenOrdersModel.data?[index]
                            .kitchenNotes?[notesIndex].kitchenType
                            ?.trim() ??
                        '',
                    style: selectedNotesTypeIndex.value == notesIndex
                        ? AppTextStyle.white8800
                        : AppTextStyle.primary8800,
                  ),
                ),
              ),
            ),
          );
        },
        separatorBuilder: (context, index) {
          return SizedBox(
            width: AppSize.width(2.7),
          );
        },
        itemCount:
            controller.kitchenOrdersModel.data?[index].kitchenNotes?.length ??
                0,
      ),
    );
  }
}
