import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/orders/presentation/views/widgets/orders_details_item.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/orders_controller.dart';

class OrdersItemsListView extends GetView<OrdersController> {
  const OrdersItemsListView({
    super.key,
    required this.index,
  });

  final int index;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsetsGeometry.only(
        top: AppSize.height(10),
        left: AppSize.width(10),
        right: AppSize.width(10),
        bottom: AppSize.height(18),
      ),
      child: SizedBox(
        height: AppSize.height(91),
        child: ListView.separated(
          itemCount: controller.getOrderAtIndex(index)?.items?.length ?? 0,
          separatorBuilder: (BuildContext context, int index) {
            return Divider(
              height: AppSize.height(7),
              color: AppColors.lavenderGray2,
            );
          },
          itemBuilder: (BuildContext context, int itemsIndexndex) {
            return OrdersDetailsItem(
              index: index,
              itemsIndex: itemsIndexndex,
            );
          },
        ),
      ),
    );
  }
}
