import 'package:flutter/material.dart';

class AppColors {
  // Light theme colors
  static const Color background = Color(0xffFFFFFF);
  static const Color primaryColor = Color(0xff342B72);
  static const Color secondaryColor = Color(0xff837DA7);
  static const Color secondry = Color(0xff9A94D7);
  static const Color third = Color(0xffE4E1F6);
  static const Color primaryWithOpacity = Color(0xffEFEDFA);
  static const Color primaryWithOpacity2 = Color(0xffD1CEE7);
  static const Color primaryWithOpacity3 = Color(0xffE9E7F6);
  static const Color white = Color(0xffFFFFFF);
  static const Color black = Color(0xff000000);
  static const Color lightGrey = Color(0xffF9F9FC);
  static const Color grey = Color(0xff9D9D9D);
  static const Color grey1 = Color(0xffD0D3DE);
  static const Color grey2 = Color(0xffE0E0E0);
  static const Color grey3 = Color(0xffC3C1DB);
  static const Color darkGrey = Color(0xff5F6164);
  static const Color error = Color(0xffAC5A5A);
  static const Color lightPink = Color(0xffF1CECB);
  static const Color lightGreen = Color(0xffECFAF6);
  static const Color greenWithOpacity = Color(0xffF6FCFA);
  static const Color lightRed = Color(0xffFFE1E1);
  static const Color red = Color(0xff921B09);
  static const Color darkRed = Color(0xff703C54);
  static const Color green = Color(0xff2CA377);
  static const Color darkGreen = Color(0xff328675);
  static const Color pink = Color(0xffFFD4D0);
  static const Color yellow = Color(0xffF3F2D7);
  static const Color frostedMint = Color(0xffBEF1E1);
  static const Color seafoamBlue = Color(0xff96CABF);
  static const Color lavenderGray = Color(0xffA6A2C2);
  static const Color lavenderGray2 = Color(0xffC4C1D3);
  static const Color tablesBackground = Color(0xffF6F6F6);
  static const Color darkGray = Color(0xff4F4F4F);
  static const Color customerSelcet = Color(0xffF8F7FF);
  static const Color mintGreen = Color(0xffD0F0EC);
  static const Color babyBlue = Color(0xffB2E9FF);
  static const Color lightYellow = Color(0xffEFEDBB);
}
