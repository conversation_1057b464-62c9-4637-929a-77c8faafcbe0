import 'package:get/get.dart';
import 'package:point_of_sale/features/auth/login/presentation/getx/controllers/login_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/orders_list_controller.dart';
import 'package:point_of_sale/features/payment/presentation/getx/controllers/pay_order_controller.dart';
import 'package:point_of_sale/features/payment/presentation/getx/controllers/payment_methods_controller.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/settings_controller.dart';

import 'payment_calculation_controller.dart';
import 'payment_method_controller.dart';
import 'payment_validation_controller.dart';

class PaymentController extends GetxController {
  final OrdersListController ordersListController =
      Get.find<OrdersListController>();
  final LoginController loginController = Get.find<LoginController>();
  final SettingsController settingsController = Get.find<SettingsController>();
  final PaymentMethodsController paymentMethodsController =
      Get.find<PaymentMethodsController>();
  final PayOrderController payOrderController = Get.find<PayOrderController>();
  final PaymentCalculationController calculationController =
      Get.find<PaymentCalculationController>();
  final PaymentMethodController methodController =
      Get.find<PaymentMethodController>();
  final PaymentValidationController validationController =
      Get.find<PaymentValidationController>();

  // Order information from navigation arguments
  final int orderId = Get.arguments?['orderId'] ?? 0;
  final int orderNumber =
      int.tryParse(Get.arguments?['orderNumber'].toString() ?? '0') ?? 0;
  final double totalPrice = Get.arguments?['totalPrice'] ?? 0;

  // Getters that delegate to sub-controllers
  String get shouldPaidAmount => calculationController.shouldPaidAmount;
  double get remaining => calculationController.remaining;
  double get vatAmount => calculationController.vatAmount;

  // Getters that delegate to method controller
  int get selectedMethodIndex => methodController.selectedIndex;
  List<double> get methodPaidAmounts => methodController.paidAmounts;

  // Delegation methods to sub-controllers
  void selectMethod(int index) => methodController.selectMethod(index);
  void onNumberPressed(String digit) => methodController.onNumberPressed(digit);
  void onClearPressed() => methodController.onClearPressed();
  void onAddValue(double value) => methodController.onAddValue(value);
  void updateMethodPaidAmount() => methodController.updateMethodPaidAmount();
  Future<void> validatePayment() =>
      validationController.validateAndProcessPayment();

  @override
  void onInit() {
    // Initialize sub-controllers with order data
    calculationController.initialize(totalPrice);
    validationController.initialize(orderId, orderNumber, totalPrice);
    methodController.initializePaymentMethods();

    super.onInit();
  }
}
