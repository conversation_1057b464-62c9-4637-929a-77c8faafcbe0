import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_colors.dart';
import '../../../../core/utils/app_text_style.dart';
import '../../../../core/utils/size_config.dart';
import '../../../../core/utils/size_utils.dart';
import '../../../home/<USER>/views/widgets/custom_drawer.dart';
import '../getx/controllers/orders_controller.dart';
import 'widgets/orders_view_body.dart';

class OrdersView extends GetView<OrdersController> {
  const OrdersView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        shape: const Border(
          bottom: BorderSide(
            color: AppColors.grey1,
            width: 1,
          ),
        ),
        toolbarHeight: AppSize.height(85),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.background,
        surfaceTintColor: AppColors.background,
        automaticallyImplyLeading: false,
        leadingWidth: AppSize.width(270),
        centerTitle: true,
        title: SizedBox(
          height: AppSize.height(36),
          width: AppSize.width(534),
          child: GetBuilder<OrdersController>(
            builder: (ordersController) {
              return Material(
                elevation: ordersController.isSearchFocused.value ? 4 : 0,
                borderRadius: BorderRadius.circular(8),
                child: TextField(
                  controller: ordersController.searchController,
                  focusNode: ordersController.searchFocusNode,
                  onChanged: ordersController.onSearchChanged,
                  onEditingComplete: () {
                    // Optional: trigger search on keyboard done
                    ordersController.applyFilters();
                  },
                  onTapOutside: (_) {
                    // Optional: unfocus on tap outside
                    ordersController.searchFocusNode.unfocus();
                  },
                  onSubmitted: ordersController.onSearchSubmitted,
                  decoration: InputDecoration(
                    hintText: 'search'.tr,
                    hintStyle: AppTextStyle.grey16600,
                    prefixIcon: IconButton(
                      onPressed: () {
                        // Optional: trigger search on icon press
                        ordersController.applyFilters();
                      },
                      icon: SvgPicture.asset(
                        height: AppSize.height(18),
                        AppAssets.searchIcon,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: AppColors.primaryColor,
                        width: 2,
                      ),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        width: 2,
                        color: AppColors.grey3,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: AppColors.grey3,
                        width: 1,
                      ),
                    ),
                    filled: true,
                    fillColor: AppColors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 0,
                      horizontal: 20,
                    ),
                  ),
                  style: AppTextStyle.primary16800,
                ),
              );
            },
          ),
        ),
        leading: Row(
          children: [
            SizedBox(
              width: AppSize.width(50),
            ),
            Builder(
              builder: (context) {
                return IconButton(
                  onPressed: () {
                    Scaffold.of(context).openDrawer();
                  },
                  icon: Icon(
                    Icons.menu,
                    size: getSize(32),
                    color: AppColors.green,
                  ),
                );
              },
            ),
            SizedBox(
              width: AppSize.width(18),
            ),
            Container(
              height: AppSize.height(34),
              width: AppSize.height(34),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.darkGreen,
              ),
              child: Center(
                child: Text(
                  controller.loginController.userName.value.isNotEmpty
                      ? controller.loginController.userName.value
                          .substring(0, 1)
                      : '',
                  style: AppTextStyle.white17600,
                ),
              ),
            ),
            SizedBox(
              width: AppSize.width(15),
            ),
            Text(
              controller.loginController.userName.value.isNotEmpty
                  ? controller.loginController.userName.value
                  : '',
              style: AppTextStyle.darkgreen14600,
            ),
          ],
        ),
        actions: [
          Padding(
            padding: EdgeInsetsDirectional.only(
              end: AppSize.width(51),
            ),
            child: Image.asset(
              AppAssets.appBarLogo,
              height: AppSize.height(50),
            ),
          ),
        ],
      ),
      drawer: Drawer(
        backgroundColor: AppColors.white,
        width: AppSize.width(372),
        child: const CustomDrawer(),
      ),
      body: const OrdersViewBody(),
    );
  }
}
