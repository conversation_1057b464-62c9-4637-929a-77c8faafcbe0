import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/settings_controller.dart';
import 'package:point_of_sale/features/settings/presentation/view/widgets/settings_switcher_item.dart';

import '../../../../../core/utils/size_config.dart';

class SettingsAddTexSection extends GetView<SettingsController> {
  const SettingsAddTexSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => SettingsSwitcherItem(
        height: AppSize.height(47),
        title: 'addTax'.tr,
        switcherValue: controller.addTax.value,
        onChanged: (value) => controller.toggleAddTax(),
      ),
    );
  }
}
