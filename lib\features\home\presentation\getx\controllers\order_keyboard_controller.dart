import 'package:get/get.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/categories_controller.dart';

enum OrderKeyboardMode { none, qty, discount, price }

class OrderKeyboardController extends GetxController {
  // Holds the current numeric input as a string.
  var currentInput = ''.obs;
  // The currently selected mode.
  var selectedMode = OrderKeyboardMode.none.obs;

  // Called when a special key (Qty, % Discount, or Price) is pressed.
  void selectMode(OrderKeyboardMode mode) {
    selectedMode.value = mode;
    currentInput.value = ''; // reset input when changing mode
  }

  // Called when a numeric or dot key is pressed.
  void addInput(String value) {
    currentInput.value += value;
    _applyInput();
  }

  // Toggle the sign of the current input.
  void toggleSign() {
    if (currentInput.value.startsWith('-')) {
      currentInput.value = currentInput.value.substring(1);
    } else {
      currentInput.value = '-${currentInput.value}';
    }
    _applyInput();
  }

  // Clear the current input and reset field to default value.
  void clearInput() {
    Get.log('OrderKeyboard: Clear button pressed');
    currentInput.value = '';
    _resetFieldToDefault();
  }

  // Reset the selected field to its default value
  void _resetFieldToDefault() {
    final categoriesController = Get.find<CategoriesController>();
    final orderController = categoriesController.orderController;
    final selectedIndex = orderController.selectedIndex.value;

    Get.log(
        'OrderKeyboard: Resetting field for index $selectedIndex, mode: ${selectedMode.value}');

    // Ensure there is a selected item.
    if (selectedIndex == -1) {
      Get.log('OrderKeyboard: No item selected');
      return;
    }

    if (orderController.localCart.isNotEmpty &&
        selectedIndex < orderController.localCart.length) {
      var currentItem = orderController.localCart[selectedIndex];
      switch (selectedMode.value) {
        case OrderKeyboardMode.qty:
          currentItem['qty'] = '1'; // Reset to default quantity
          _recalculateItemTotal(currentItem);
          break;
        case OrderKeyboardMode.discount:
          currentItem['discount'] = '0'; // Reset to no discount
          _recalculateItemTotal(currentItem);
          break;
        case OrderKeyboardMode.price:
          // Reset to original price
          final originalPrice =
              currentItem['originalPrice'] ?? currentItem['price'] ?? '0';
          currentItem['price'] = originalPrice;
          _recalculateItemTotal(currentItem);
          break;
        case OrderKeyboardMode.none:
          break;
      }
      orderController.localCart[selectedIndex] = currentItem;
      orderController.localCart.refresh();
    }
  }

  // Apply the current input to the selected order item.
  void _applyInput() {
    // Access the CategoriesController (which holds the orderController)
    final categoriesController = Get.find<CategoriesController>();
    final orderController = categoriesController.orderController;
    final selectedIndex = orderController.selectedIndex.value;

    // Ensure there is a selected item.
    if (selectedIndex == -1) return;

    // Example: if using a local cart to store order items.
    if (orderController.localCart.isNotEmpty &&
        selectedIndex < orderController.localCart.length) {
      var currentItem = orderController.localCart[selectedIndex];
      switch (selectedMode.value) {
        case OrderKeyboardMode.qty:
          if (currentInput.value.isNotEmpty) {
            final qty = int.tryParse(currentInput.value) ?? 1;
            if (qty > 0) {
              currentItem['qty'] = qty.toString();
              _recalculateItemTotal(currentItem);
            }
          }
          break;
        case OrderKeyboardMode.discount:
          if (currentInput.value.isNotEmpty) {
            final discount = double.tryParse(currentInput.value) ?? 0;
            if (discount >= 0 && discount <= 100) {
              currentItem['discount'] = discount.toString();
              _recalculateItemTotal(currentItem);
            }
          }
          break;
        case OrderKeyboardMode.price:
          if (currentInput.value.isNotEmpty) {
            final price = double.tryParse(currentInput.value) ?? 0;
            if (price >= 0) {
              currentItem['price'] = price.toString();
              _recalculateItemTotal(currentItem);
            }
          }
          break;
        case OrderKeyboardMode.none:
          break;
      }
      orderController.localCart[selectedIndex] = currentItem;
      orderController.localCart.refresh();
    }
  }

  // Recalculate item total price including discount
  void _recalculateItemTotal(Map<String, String> item) {
    final qty = int.tryParse(item['qty'] ?? '1') ?? 1;
    final unitPrice = double.tryParse(item['price'] ?? '0') ?? 0;
    final discountPercent = double.tryParse(item['discount'] ?? '0') ?? 0;

    // Calculate subtotal before discount
    final subtotal = unitPrice * qty;

    // Calculate discount amount
    final discountAmount = subtotal * (discountPercent / 100);

    // Calculate final total after discount
    final finalTotal = subtotal - discountAmount;

    // Update the item with calculated values
    item['subtotal'] = subtotal.toStringAsFixed(2);
    item['discountAmount'] = discountAmount.toStringAsFixed(2);
    item['totalPrice'] = finalTotal.toStringAsFixed(2);
  }
}
