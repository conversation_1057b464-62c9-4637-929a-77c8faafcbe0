import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/kitchen_orders_controller.dart';

class NotesContent extends GetView<KitchenOrdersController> {
  final int index;
  final RxInt selectedNotesTypeIndex;
  final PageController pageController;

  const NotesContent({
    super.key,
    required this.selectedNotesTypeIndex,
    required this.pageController,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: AppSize.height(120),
      child: PageView.builder(
        physics: const NeverScrollableScrollPhysics(),
        scrollDirection: Axis.horizontal,
        controller: pageController,
        itemBuilder: (context, notesIndex) {
          return Padding(
            padding: EdgeInsetsDirectional.symmetric(
              horizontal: AppSize.width(10),
              vertical: AppSize.height(10),
            ),
            child: AnimatedContainer(
              duration: Duration(milliseconds: 200),
              height: AppSize.height(100),
              width: AppSize.width(50),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: AppColors.white,
                border: Border.all(
                  color: AppColors.secondry,
                ),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(
                  vertical: AppSize.height(7),
                  horizontal: AppSize.width(14),
                ),
                child: SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: Text(
                    controller.kitchenOrdersModel.data?[index]
                            .kitchenNotes?[notesIndex].kitchenNotes ??
                        '',
                    style: selectedNotesTypeIndex.value == notesIndex
                        ? AppTextStyle.primary12800
                        : AppTextStyle.white12600,
                  ),
                ),
              ),
            ),
          );
        },
        itemCount:
            controller.kitchenOrdersModel.data?[index].kitchenNotes?.length ??
                0,
      ),
    );
  }
}
