import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/routes/app_pages.dart';
import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../../../../core/utils/size_utils.dart';
import '../../../../orders/presentation/getx/controllers/orders_controller.dart';

class OrderActionsMenu extends GetView<OrdersController> {
  final String? orderId;
  final int index;

  const OrderActionsMenu({
    super.key,
    required this.orderId,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: AlignmentDirectional.topEnd,
      child: PopupMenuButton<String>(
        padding: EdgeInsets.zero,
        constraints: BoxConstraints(minWidth: 0, minHeight: 0),
        itemBuilder: (context) => [
          PopupMenuItem<String>(
            onTap: () {
              Get.toNamed(Routes.print, arguments: {
                'orderId': controller.getOrderAtIndex(index)?.id,
                'orderNumber': controller.getOrderAtIndex(index)?.orderNo,
                'totalPrice': num.parse(
                    controller.getOrderAtIndex(index)?.totalAmount.toString() ??
                        ''),
              });
              // Get.find<ChangeOrderStatusController>().changeOrderStatus(
              //   orderId ?? '',
              //   '3',
              // );
              // controller.getKitchenOrders();
            },
            child: Text(
              'printOrder'.tr,
              style: AppTextStyle.primary12800,
            ),
          ),
        ],
        child: Padding(
          padding: EdgeInsetsDirectional.only(
            top: AppSize.height(5),
            end: AppSize.width(5),
          ),
          child: Icon(
            Icons.more_vert,
            color: AppColors.primaryColor,
            size: getSize(18),
          ),
        ),
      ),
    );
  }
}
