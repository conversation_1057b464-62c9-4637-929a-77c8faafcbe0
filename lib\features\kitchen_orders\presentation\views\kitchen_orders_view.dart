import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_colors.dart';
import '../../../../core/utils/size_config.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../home/<USER>/views/widgets/custom_drawer.dart';
import '../getx/controllers/kitchen_orders_controller.dart';
import 'widgets/kitchen_order_card.dart';
import 'widgets/kitchen_orders_app_bar.dart';

class KitchenOrdersView extends GetView<KitchenOrdersController> {
  const KitchenOrdersView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const KitchenOrdersAppBar(),
      drawer: const Drawer(
        backgroundColor: AppColors.white,
        child: CustomDrawer(),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          controller.getKitchenOrders();
        },
        child: Padding(
          padding: EdgeInsetsDirectional.symmetric(
            horizontal: AppSize.width(47),
            vertical: AppSize.height(15),
          ),
          child: Column(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: AppColors.lavenderGray,
                    ),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Obx(
                    () {
                      if (controller.loading.isTrue) {
                        return const Center(
                          child: LoadingWidget(),
                        );
                      }
                      return SingleChildScrollView(
                        padding: EdgeInsets.symmetric(
                          vertical: AppSize.height(24),
                          horizontal: AppSize.width(24),
                        ),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            final crossAxisCount = 4;
                            final spacing = AppSize.width(24);
                            final itemWidth = (constraints.maxWidth -
                                    (spacing * (crossAxisCount - 1))) /
                                crossAxisCount;

                            return Wrap(
                              spacing: spacing,
                              runSpacing: AppSize.height(24),
                              children: List.generate(
                                controller.kitchenOrdersModel.data?.length ?? 0,
                                (index) {
                                  return KitchenOrderCard(
                                    index: index,
                                    itemWidth: itemWidth,
                                  );
                                },
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
