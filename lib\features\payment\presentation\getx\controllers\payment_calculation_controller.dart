import 'package:get/get.dart';

import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/widgets/failed_snack_bar.dart';
import '../../../../auth/login/presentation/getx/controllers/login_controller.dart';
import '../../../../home/<USER>/getx/controllers/orders_list_controller.dart';
import '../../../../settings/presentation/getx/controllers/settings_controller.dart';

class PaymentCalculationController extends GetxController {
  final LoginController loginController = Get.find<LoginController>();
  final SettingsController settingsController = Get.find<SettingsController>();
  final OrdersListController ordersListController =
      Get.find<OrdersListController>();
  // Reactive outputs
  final RxString _shouldPaidAmount = '0.00'.obs;
  final RxDouble _remaining = 0.0.obs;
  final RxDouble _vatAmount = 0.0.obs;
  final cashDataSource = Get.find<CashDataSource>();
  // Public getters
  String get shouldPaidAmount => _shouldPaidAmount.value;
  double get remaining => _remaining.value;
  double get vatAmount => _vatAmount.value;

  // Inputs
  late double totalPrice = double.parse(ordersListController
          .ordersListModel.data?[ordersListController.order.value].totalAmount
          .toString() ??
      '');
  RxList<double> methodPaidAmounts = <double>[].obs;

  void initialize(double price) {
    totalPrice = price;
    _recalculateAll();
  }

  void updateMethodPaidAmounts(List<double> amounts) {
    methodPaidAmounts.value = amounts;
    _updateRemaining(); // only remaining changes
  }

  void _recalculateAll() {
    // Get addTax setting from cache (consistent with order creation)
    final invoiceData = cashDataSource.getInvoiceData();
    final addTax = invoiceData['addTax'] as bool;
    double pct = _getVatPercentage();
    double base = totalPrice.toDouble();

    if (addTax) {
      // When addTax is true: ADD VAT to the base price
      double vat = base * (pct / 100);
      double total = base + vat;
      _vatAmount.value = vat;
      _shouldPaidAmount.value = total.toStringAsFixed(2);

      _updateRemaining(total);
    } else {
      // When addTax is false: Price already includes VAT, use as is
      double total = base;
      double vat = total * pct / (100 + pct); // Extract VAT for display

      _vatAmount.value = vat;
      _shouldPaidAmount.value = total.toStringAsFixed(2);

      _updateRemaining(total);
    }
  }

  void _updateRemaining([double? totalOverride]) {
    // If totalOverride isn't passed, parse the _shouldPaidAmount
    double total =
        totalOverride ?? double.tryParse(_shouldPaidAmount.value) ?? 0.0;

    double paid = methodPaidAmounts.fold(0.0, (sum, a) => sum + a);
    _remaining.value = total - paid;
  }

  double _getVatPercentage() {
    final invoiceData = cashDataSource.getInvoiceData();

    final raw = invoiceData['vat'] as String;
    return double.tryParse(raw) ?? 0.0;
  }

  bool validateTotalPayment() {
    double paid = methodPaidAmounts.fold(0.0, (s, a) => s + a);
    double req = double.tryParse(_shouldPaidAmount.value) ?? 0.0;

    if (paid.toStringAsFixed(2) != req.toStringAsFixed(2)) {
      failedSnaskBar(
          'The total paid (${paid.toStringAsFixed(2)}) does not equal '
          'the required total (${req.toStringAsFixed(2)}). '
          'Remaining: ${remaining.toStringAsFixed(2)}');
      return false;
    }
    return true;
  }

  double calculateServerAmount(double paymentAmount) {
    // we already include VAT in paymentAmount
    return paymentAmount;
  }

  @override
  void onInit() {
    // Whenever the VAT‐toggle flips, recalc everything
    ever(settingsController.addTax, (_) => _recalculateAll());
    // Whenever the paid‐methods list changes, update remaining
    ever(methodPaidAmounts, (_) => _updateRemaining());
    super.onInit();
  }
}
