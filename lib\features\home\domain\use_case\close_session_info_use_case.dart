import 'package:dartz/dartz.dart';

import '../../../../core/errors/failure.dart';
import '../../../../core/services/use_case.dart';
import '../../data/models/close_session_info_model.dart';
import '../entitis/close_session_info_entity.dart';
import '../repo/close_session_info_repo.dart';

class CloseSessionInfoUseCase
    extends UseCase<CloseSessionInfoModel, CloseSessionInfoEntity> {
  final CloseSessionInfoRepo closeSessionInfoRepo;

  CloseSessionInfoUseCase(this.closeSessionInfoRepo);

  @override
  Future<Either<Failure, CloseSessionInfoModel>> call(
      CloseSessionInfoEntity params) async {
    params.loading(true);
    final result = closeSessionInfoRepo.closeSessionInfo(params);
    result.then((value) {
      params.loading(false);
    });
    return result;
  }
}
