import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:point_of_sale/core/services/session_manager.dart';
import 'package:point_of_sale/core/services/cash_data_source.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/core/utils/size_config.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/drawer_controller.dart';

import '../../../../../core/utils/app_assets.dart';
import '../../../../../core/utils/app_colors.dart';

class SessionContinuationController extends GetxController {
  final CashDataSource cashDataSource = Get.find<CashDataSource>();
  final AppDrawerController appDrawerController =
      Get.find<AppDrawerController>();

  // Keys for storage
  static const String _keyFromCache = 'navigation_from_cache';
  static const String _keyJustCreatedSession = 'just_created_session';
  void markNavigationFromCache() {
    cashDataSource.box.write(_keyFromCache, true);
  }

  static void markSessionAsJustCreated() {
    final box = GetStorage();
    box.write(_keyJustCreatedSession, true);
    box.write(_keyFromCache, false);
  }

  bool shouldShowContinuationDialog() {
    final fromCache = cashDataSource.box.read(_keyFromCache) ?? false;
    final justCreatedSession =
        cashDataSource.box.read(_keyJustCreatedSession) ?? false;
    final hasActiveSession = SessionManager.hasActiveSession;

    if (justCreatedSession) {
      cashDataSource.box.write(_keyJustCreatedSession, false);
      return false;
    }

    final shouldShow = hasActiveSession && fromCache;
    if (fromCache) {
      cashDataSource.box.write(_keyFromCache, false);
    }
    return shouldShow;
  }

  void showSessionContinuationDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: [
            SizedBox(
              height: AppSize.height(150),
              width: AppSize.width(200),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    height: AppSize.height(32),
                  ),
                  Text(
                    'sessionIsAlreadyActive'.tr,
                    style: AppTextStyle.primary18700,
                  ),
                  Spacer(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () {
                          Get.back();
                        },
                        child: Text(
                          'stayInSession'.tr,
                          style: AppTextStyle.primary16700,
                        ),
                      ),
                      Spacer(),
                      InkWell(
                        onTap: () {
                          Get.back();
                          appDrawerController.prepareCloseSession(context);
                        },
                        child: Text(
                          'closeSession'.tr,
                          style: AppTextStyle.red16700,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: AppSize.height(10),
                  ),
                ],
              ),
            ),
            Positioned(
              top: -AppSize.height(75),
              child: Container(
                height: AppSize.height(100),
                width: AppSize.width(100),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.white,
                ),
                child: Padding(
                  padding: EdgeInsets.all(
                    AppSize.height(10),
                  ),
                  child: Center(
                    child: Image.asset(
                      fit: BoxFit.contain,
                      AppAssets.continueImage,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      barrierDismissible: false, // Prevent dismissing by tapping outside
    );
  }
}
