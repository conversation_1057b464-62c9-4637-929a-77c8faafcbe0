import 'package:dartz/dartz.dart';
import 'package:point_of_sale/core/errors/failure.dart';
import 'package:point_of_sale/core/services/use_case.dart';
import 'package:point_of_sale/features/orders/data/models/change_order_status_model.dart';
import 'package:point_of_sale/features/orders/domain/entitis/change_order_status_entity.dart';
import 'package:point_of_sale/features/orders/domain/repo/change_order_status_repo.dart';

class ChangeOrderStatusUseCase
    extends UseCase<ChangeOrderStatusModel, ChangeOrderStatusEntity> {
  final ChangeOrderStatusRepo changeOrderStatusRepo;
  ChangeOrderStatusUseCase(this.changeOrderStatusRepo);
  @override
  Future<Either<Failure, ChangeOrderStatusModel>> call(
      ChangeOrderStatusEntity params) {
    params.loading(true);
    final result = changeOrderStatusRepo.changeOrderStatus(params);
    result.then((value) {
      params.loading(false);
    });
    return result;
  }
}
