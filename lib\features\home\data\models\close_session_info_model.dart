// To parse this JSON data, do
//
//     final closeSessionInfoModel = closeSessionInfoModelFromJson(jsonString);

import 'dart:convert';

CloseSessionInfoModel closeSessionInfoModelFromJson(String str) =>
    CloseSessionInfoModel.fromJson(json.decode(str));

String closeSessionInfoModelToJson(CloseSessionInfoModel data) =>
    json.encode(data.toJson());

class CloseSessionInfoModel {
  final int? status;
  final String? result;
  final Data? data;

  CloseSessionInfoModel({
    this.status,
    this.result,
    this.data,
  });

  factory CloseSessionInfoModel.fromJson(Map<String, dynamic> json) =>
      CloseSessionInfoModel(
        status: json["status"],
        result: json["result"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "result": result,
        "data": data?.toJson(),
      };
}

class Data {
  final String? openSession;
  final String? cashIn;
  final String? cashOut;
  final List<Sale>? sales;
  final int? ordersCount;
  final double? ordersTotal;

  Data({
    this.openSession,
    this.cashIn,
    this.cashOut,
    this.sales,
    this.ordersCount,
    this.ordersTotal,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        openSession: json["open_session"]?.toString(),
        cashIn: json["cash_in"]?.toString(),
        cashOut: json["cash_out"]?.toString(),
        sales: json["sales"] == null
            ? []
            : List<Sale>.from(json["sales"]!.map((x) => Sale.fromJson(x))),
        ordersCount: json["orders_count"],
        ordersTotal: json["orders_total"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "open_session": openSession,
        "cash_in": cashIn,
        "cash_out": cashOut,
        "sales": sales == null
            ? []
            : List<dynamic>.from(sales!.map((x) => x.toJson())),
        "orders_count": ordersCount,
        "orders_total": ordersTotal,
      };
}

class Sale {
  final int? paymentMethodId;
  final String? totalAmount;
  final PaymentMethod? paymentMethod;

  Sale({
    this.paymentMethodId,
    this.totalAmount,
    this.paymentMethod,
  });

  factory Sale.fromJson(Map<String, dynamic> json) => Sale(
        paymentMethodId: json["payment_method_id"],
        totalAmount: json["total_amount"]?.toString(),
        paymentMethod: json["payment_method"] == null
            ? null
            : PaymentMethod.fromJson(json["payment_method"]),
      );

  Map<String, dynamic> toJson() => {
        "payment_method_id": paymentMethodId,
        "total_amount": totalAmount,
        "payment_method": paymentMethod?.toJson(),
      };
}

class PaymentMethod {
  final int? id;
  final bool? isActive;
  final String? tenantId;
  final String? companyId;
  final String? branchId;
  final String? createdBy;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? paymentType;

  PaymentMethod({
    this.id,
    this.isActive,
    this.tenantId,
    this.companyId,
    this.branchId,
    this.createdBy,
    this.createdAt,
    this.updatedAt,
    this.paymentType,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) => PaymentMethod(
        id: json["id"],
        isActive: json["is_active"],
        tenantId: json["tenant_id"],
        companyId: json["company_id"],
        branchId: json["branch_id"],
        createdBy: json["created_by"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        paymentType: json["payment_type"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "is_active": isActive,
        "tenant_id": tenantId,
        "company_id": companyId,
        "branch_id": branchId,
        "created_by": createdBy,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "payment_type": paymentType,
      };
}
