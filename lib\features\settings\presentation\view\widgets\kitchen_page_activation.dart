import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/settings/presentation/view/widgets/settings_switcher_item.dart';

import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/settings_controller.dart';

class KitchenPageActivation extends GetView<SettingsController> {
  const KitchenPageActivation({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => SettingsSwitcherItem(
        height: AppSize.height(47),
        title: 'activrateKitchenPage'.tr,
        switcherValue: controller.kitchenPageActive.value,
        onChanged: (value) => controller.toggleKitchenPageActive(),
      ),
    );
  }
}
