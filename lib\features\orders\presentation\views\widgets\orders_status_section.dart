import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/orders_controller.dart';

class OrdersStatusSection extends GetView<OrdersController> {
  const OrdersStatusSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: AppSize.height(37),
      width: AppSize.width(580),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: controller.orderStateList.length,
        separatorBuilder: (BuildContext context, int index) {
          return SizedBox(
            width: AppSize.width(5),
          );
        },
        itemBuilder: (BuildContext context, int index) {
          return Obx(
            () => InkWell(
              onTap: () {
                controller.changeOrderStateIndex(index);
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: AppSize.height(37),
                width: AppSize.width(92),
                decoration: BoxDecoration(
                  color: controller.orderStateIndex.value == index
                      ? AppColors.primaryColor
                      : AppColors.white,
                  border: Border(
                    top: BorderSide(
                      color: controller.orderStateIndex.value == index
                          ? AppColors.primaryColor
                          : AppColors.lavenderGray,
                    ),
                    left: BorderSide(
                      color: controller.orderStateIndex.value == index
                          ? AppColors.primaryColor
                          : AppColors.lavenderGray,
                    ),
                    right: BorderSide(
                      color: controller.orderStateIndex.value == index
                          ? AppColors.primaryColor
                          : AppColors.lavenderGray,
                    ),
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(6),
                    topRight: Radius.circular(6),
                  ),
                ),
                child: Center(
                  child: Text(
                    controller.orderStateList[index],
                    style: controller.orderStateIndex.value == index
                        ? AppTextStyle.white14700
                        : AppTextStyle.primary14700,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
