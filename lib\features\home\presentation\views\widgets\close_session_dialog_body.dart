import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/features/home/<USER>/views/widgets/close_session_buttons.dart';
import 'package:point_of_sale/features/home/<USER>/views/widgets/close_session_text_fields.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../../../../core/widgets/loading_widget.dart';
import '../../getx/controllers/close_session_controller.dart';
import 'close_register_money_counter.dart';

class CloseSessionDialogBody extends GetView<CloseSessionController> {
  const CloseSessionDialogBody({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: AppSize.height(480),
      width: AppSize.width(725),
      child: Obx(
        () => controller.closeSessionInfoController.loading.isTrue
            ? const Center(
                child: LoadingWidget(),
              )
            : Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        height: AppSize.height(30),
                        width: AppSize.width(81),
                        decoration: BoxDecoration(
                          color: AppColors.third,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '${controller.closeSessionInfoController.closeSessionInfoModel.data?.ordersCount ?? '0'}',
                              style: AppTextStyle.primary16800,
                            ),
                            Text(
                              ' ${'order'.tr}',
                              style: AppTextStyle.primary16800,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        width: AppSize.width(52),
                      ),
                      Container(
                        height: AppSize.height(30),
                        width: AppSize.width(81),
                        decoration: BoxDecoration(
                          color: AppColors.third,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '${controller.closeSessionInfoController.closeSessionInfoModel.data?.ordersTotal?.roundToDouble() ?? '0.00'}',
                              style: AppTextStyle.primary16800,
                            ),
                            Text(
                              ' ${'SR'.tr}',
                              style: AppTextStyle.primary16800,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: AppSize.height(21),
                  ),
                  const Divider(
                    thickness: 1.5,
                  ),
                  CloseRegisterMoneyCounter(
                    textStyle: AppTextStyle.primary18800,
                    itemName: 'cash'.tr,
                    itemPrice: controller.cashItemPrice,
                  ),
                  CloseRegisterMoneyCounter(
                    textStyle: AppTextStyle.primary16600,
                    itemName: 'opening'.tr,
                    itemPrice: controller.closeSessionInfoController
                            .closeSessionInfoModel.data?.openSession
                            ?.toString() ??
                        "0.00",
                  ),
                  CloseRegisterMoneyCounter(
                    textStyle: AppTextStyle.primary16600,
                    itemName: 'cashIn'.tr,
                    itemPrice: controller.closeSessionInfoController
                            .closeSessionInfoModel.data?.cashIn
                            .toString() ??
                        '0.00',
                  ),
                  CloseRegisterMoneyCounter(
                    textStyle: AppTextStyle.primary16600,
                    itemName: 'cashOut'.tr,
                    itemPrice: controller.closeSessionInfoController
                            .closeSessionInfoModel.data?.cashOut
                            .toString() ??
                        '0.00',
                  ),
                  CloseRegisterMoneyCounter(
                    textStyle: AppTextStyle.primary16600,
                    itemName: 'counted'.tr,
                    itemPrice: controller.closeCashValue.value.isEmpty
                        ? '0.00'
                        : controller.closeCashValue.value,
                  ),
                  CloseRegisterMoneyCounter(
                    textStyle: AppTextStyle.primary16600,
                    itemName: 'difference'.tr,
                    itemPrice: controller.difference.toStringAsFixed(2),
                  ),
                  Divider(
                    thickness: 1.5,
                  ),
                  CloseRegisterMoneyCounter(
                    textStyle: AppTextStyle.primary18800,
                    itemName: 'card'.tr,
                    itemPrice: controller.cardItemPrice,
                  ),
                  const Divider(
                    thickness: 1.5,
                  ),
                  const ClosingSessionFields(),
                  SizedBox(
                    height: AppSize.height(16),
                  ),
                  const CloseSessionButtom(),
                ],
              ),
      ),
    );
  }
}
   // CloseRegisterMoneyCounter(
                          //   textStyle: AppTextStyle.primary16600,
                          //   itemName: 'counted'.tr,
                          //   itemPrice: '0.00',
                          // ),
                          // CloseRegisterMoneyCounter(
                          //   textStyle: AppTextStyle.primary16600,
                          //   itemName: 'difference'.tr,
                          //   itemPrice: '0.00',
                          // ),
                          // const Divider(
                          //   thickness: 1.5,
                          // ),
                          // CloseRegisterMoneyCounter(
                          //   textStyle: AppTextStyle.primary18800,
                          //   itemName: 'customerAccount'.tr,
                          //   itemPrice: '0.00',
                          // ),
                          // CloseRegisterMoneyCounter(
                          //   textStyle: AppTextStyle.primary16600,
                          //   itemName: 'counted'.tr,
                          //   itemPrice: '0.00',
                          // ),
                          // CloseRegisterMoneyCounter(
                          //   textStyle: AppTextStyle.primary16600,
                          //   itemName: 'difference'.tr,
                          //   itemPrice: '0.00',
                          // ),
