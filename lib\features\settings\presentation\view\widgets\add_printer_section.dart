import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';

import '../../../../../core/utils/size_config.dart';

class AddPrinterSection extends GetView<PrintersIPAddressController> {
  const AddPrinterSection({super.key});

  void _addPrinter() {
    final ip = controller.ipController.text.trim();
    if (controller.isValidIP(ip)) {
      controller.addPrinter(ip, controller.selectedPrinterType.value);
      controller.ipController.clear();
    } else {
      failedSnaskBar('pleaseEnterAvalidIPAddress'.tr);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: AppSize.height(12),
      ),
      child: Column(
        children: [
          // Dropdown for printer type
          Row(
            children: [
              Expanded(
                child: Obx(
                  () => Container(
                    height: AppSize.height(47),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.lavenderGray,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<PrinterType>(
                        value: controller.selectedPrinterType.value,
                        isExpanded: true,
                        padding: EdgeInsets.symmetric(
                          horizontal: AppSize.width(12),
                        ),
                        items: PrinterType.values.map((PrinterType type) {
                          return DropdownMenuItem<PrinterType>(
                            value: type,
                            child: Text(
                              type.translatedDisplayName,
                              style: AppTextStyle.primary16800,
                            ),
                          );
                        }).toList(),
                        onChanged: (PrinterType? newValue) {
                          if (newValue != null) {
                            controller.setSelectedPrinterType(newValue);
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppSize.height(12)),
          // IP address input and add button
          Row(
            children: [
              Expanded(
                child: TextField(
                  focusNode: controller.ipFocusNode,
                  onSubmitted: (value) => _addPrinter(),
                  onChanged: (value) {
                    if (value.isEmpty) {
                      controller.ipFocusNode.unfocus();
                    }
                  },
                  onEditingComplete: () => _addPrinter(),
                  onTapOutside: (event) {
                    controller.ipFocusNode.unfocus();
                  },
                  controller: controller.ipController,
                  decoration: InputDecoration(
                    hintText: 'printerIPAddress'.tr,
                    border: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColors.lavenderGray,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColors.primaryColor,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
              SizedBox(width: AppSize.width(12)),
              InkWell(
                onTap: _addPrinter,
                child: Container(
                  height: AppSize.height(47),
                  width: AppSize.width(112),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Text(
                      'add'.tr,
                      style: AppTextStyle.white18800,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
