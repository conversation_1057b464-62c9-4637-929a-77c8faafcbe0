import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../../../../core/utils/size_utils.dart';
import '../../getx/controllers/change_order_status_controller.dart';
import '../../getx/controllers/orders_controller.dart';

class OrderPopupMenu extends GetView<OrdersController> {
  const OrderPopupMenu({super.key, required this.index});
  final int index;
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: AlignmentDirectional.topEnd,
      child: PopupMenuButton<String>(
        padding: EdgeInsets.zero,
        constraints: BoxConstraints(minWidth: 0, minHeight: 0),
        itemBuilder: (context) => [
          PopupMenuItem<String>(
            onTap: () {
              Get.find<ChangeOrderStatusController>().changeOrderStatus(
                controller.getOrderAtIndex(index)?.id.toString() ?? '',
                '4',
              );
              controller.getOrdersList();
            },
            child: Text(
              'pendOrder'.tr,
              style: AppTextStyle.primary12800,
            ),
          ),
          PopupMenuItem<String>(
            onTap: () {
              Get.find<ChangeOrderStatusController>().changeOrderStatus(
                controller.getOrderAtIndex(index)?.id.toString() ?? '',
                '5',
              );
              controller.getOrdersList();
            },
            child: Text(
              'cancelOrder'.tr,
              style: AppTextStyle.primary12800,
            ),
          ),
        ],
        child: Padding(
          padding: EdgeInsetsDirectional.only(
            top: AppSize.height(5),
            end: AppSize.width(5),
          ),
          child: Icon(
            Icons.more_vert,
            color: AppColors.primaryColor,
            size: getSize(18),
          ),
        ),
      ),
    );
  }
}
