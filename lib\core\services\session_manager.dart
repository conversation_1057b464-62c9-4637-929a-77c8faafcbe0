import 'package:get/get.dart';
import 'package:point_of_sale/core/services/cash_data_source.dart';

/// Session Manager - Utility class for managing session data throughout the app
class SessionManager {
  static final CashDataSource _cashDataSource = Get.find<CashDataSource>();

  /// Check if there's an active session
  static bool get hasActiveSession => _cashDataSource.isSessionActive();

  /// Get current session ID
  static int? get currentSessionId => _cashDataSource.getCurrentSessionId();

  /// Get current session code
  static String? get currentSessionCode => _cashDataSource.getCurrentSessionCode();

  /// Get all session data
  static Map<String, dynamic> get sessionData => _cashDataSource.getSessionData();

  /// Get session open cash amount
  static String get openCash => sessionData['openCash'] ?? '0';

  /// Get session open notes
  static String? get openNotes => sessionData['openNotes'];

  /// Get session creation date
  static String? get createdAt => sessionData['createdAt'];

  /// Get session last update date
  static String? get updatedAt => sessionData['updatedAt'];

  /// Save session data
  static void saveSession({
    required int sessionId,
    required String sessionCode,
    required String openCash,
    String? openNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    _cashDataSource.saveSessionData(
      sessionId: sessionId,
      sessionCode: sessionCode,
      openCash: openCash,
      openNotes: openNotes,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Close current session
  static void closeSession() {
    _cashDataSource.clearSessionData();
    Get.log('SessionManager: Session closed and data cleared');
  }

  /// Update session status
  static void updateSessionStatus(bool isActive) {
    _cashDataSource.updateSessionStatus(isActive);
  }

  /// Get session info for display
  static String getSessionInfo() {
    if (!hasActiveSession) return 'No active session';
    
    final sessionCode = currentSessionCode ?? 'Unknown';
    final sessionId = currentSessionId ?? 0;
    return 'Session: $sessionCode (ID: $sessionId)';
  }

  /// Check if session ID matches
  static bool isCurrentSession(int sessionId) {
    return currentSessionId == sessionId;
  }

  /// Validate session data
  static bool validateSession() {
    if (!hasActiveSession) return false;
    if (currentSessionId == null || currentSessionId! <= 0) return false;
    if (currentSessionCode == null || currentSessionCode!.isEmpty) return false;
    return true;
  }
}
