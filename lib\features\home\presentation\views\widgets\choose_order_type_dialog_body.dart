import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/home/<USER>/views/widgets/choose_table_dialog.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../../../../core/widgets/confirm_and_cancel_buttons.dart';
import '../../../../../core/widgets/failed_snack_bar.dart';
import '../../getx/controllers/create_new_order_controller.dart';

class ChooseOrderTypeDialogBody extends GetView<CreateNewOrderController> {
  const ChooseOrderTypeDialogBody({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: AppSize.height(335),
      width: AppSize.width(725),
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: AppSize.height(16),
          horizontal: AppSize.width(20),
        ),
        child: Column(
          children: [
            Align(
              alignment: AlignmentDirectional.topStart,
              child: Text(
                'orderType'.tr,
                style: AppTextStyle.primary16800,
              ),
            ),
            SizedBox(
              height: AppSize.height(28),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              spacing: AppSize.width(12),
              children: [
                InkWell(
                  onTap: () {
                    controller.setOrderType(0);
                  },
                  child: Obx(
                    () => Container(
                      height: AppSize.height(129),
                      width: AppSize.width(107),
                      decoration: BoxDecoration(
                        color: controller.orderType.value == 0
                            ? AppColors.primaryColor
                            : AppColors.white,
                        border: Border.all(
                          width: 1.5,
                          color: AppColors.primaryColor,
                        ),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Center(
                        child: Text(
                          'takeAway'.tr,
                          style: controller.orderType.value == 0
                              ? AppTextStyle.white14800
                              : AppTextStyle.primary14800,
                        ),
                      ),
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    controller.setOrderType(1);
                  },
                  child: Obx(
                    () => Container(
                      height: AppSize.height(129),
                      width: AppSize.width(107),
                      decoration: BoxDecoration(
                        color: controller.orderType.value == 1
                            ? AppColors.primaryColor
                            : AppColors.white,
                        border: Border.all(
                          width: 1.5,
                          color: AppColors.primaryColor,
                        ),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Center(
                        child: Text(
                          'dineIn'.tr,
                          style: controller.orderType.value == 1
                              ? AppTextStyle.white14800
                              : AppTextStyle.primary14800,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Spacer(),
            ConfirmAndCancelButtons(
              cancelTap: () {
                controller.setOrderType(-1);
                Get.back();
              },
              confirmTap: () {
                Get.back();

                if (controller.orderType.value == 0) {
                  controller.getCreateNewOrder();
                } else if (controller.orderType.value == 1) {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return const ChooseTableDialog();
                    },
                  );
                } else {
                  failedSnaskBar('chooseOrderType'.tr);
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
