import 'package:get/get.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/services/api_service.dart';
import '../../../../core/utils/constants.dart';
import '../../domain/entitis/close_session_info_entity.dart';
import '../models/close_session_info_model.dart';

abstract class CloseSessionInfoRemoteDataSource {
  Future<CloseSessionInfoModel> closeSessionInfo(
      CloseSessionInfoEntity closeSessionInfoEntity);
}

class CloseSessionInfoRemoteDataSourceImpl
    implements CloseSessionInfoRemoteDataSource {
  ApiService apiService;
  CloseSessionInfoRemoteDataSourceImpl(this.apiService);
  @override
  Future<CloseSessionInfoModel> closeSessionInfo(
      CloseSessionInfoEntity closeSessionInfoEntity) async {
    try {
      final url =
          '${Constants.baseUrl}sales/pos/sessions/summary/${closeSessionInfoEntity.sessionId}';
      Get.log('CloseSessionInfoRemoteDataSource: Making request to: $url');

      final result = await apiService.getRequest(url);
      final responseData = result.data;

      Get.log(
          'CloseSessionInfoRemoteDataSource: Response status: ${result.statusCode}');
      Get.log('CloseSessionInfoRemoteDataSource: Response data: $responseData');

      if (result.statusCode == 200 || result.statusCode == 201) {
        return CloseSessionInfoModel.fromJson(result.data);
      } else {
        throw AppExceptions(
          message:
              responseData['message'] ?? 'Close session info request failed',
          statusCode: result.statusCode,
          data: responseData,
        );
      }
    } catch (e) {
      Get.log('CloseSessionInfoRemoteDataSource: Error occurred: $e');
      if (e is AppExceptions) {
        rethrow;
      }
      throw AppExceptions(
        message: 'Failed to get close session info: $e',
        statusCode: 500,
        data: null,
      );
    }
  }
}
