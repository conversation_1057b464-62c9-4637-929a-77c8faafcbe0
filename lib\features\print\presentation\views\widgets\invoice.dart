import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/print/presentation/views/widgets/company_info_table.dart';
import 'package:point_of_sale/features/print/presentation/views/widgets/invoice_column_headers.dart';
import 'package:point_of_sale/features/print/presentation/views/widgets/invoice_item_row.dart';
import 'package:point_of_sale/features/print/presentation/views/widgets/invoice_number_box.dart';
import 'package:point_of_sale/features/print/presentation/views/widgets/invoice_title.dart';
import 'package:point_of_sale/features/print/presentation/views/widgets/invoice_total_row.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/utils/app_colors.dart';
import '../../getx/controllers/print_controller.dart';

class Invoice extends GetView<PrintController> {
  const Invoice({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final cashDataSource = Get.find<CashDataSource>();
    final invoiceData = cashDataSource.getInvoiceData();
    final logoUrl = invoiceData['logo'] as String;
    final commerce = invoiceData['commerce'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final restaurntName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final vat = invoiceData['vat'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;
    final invoiceNumber = controller.orderNumber.toString();

    // final double subtotal = controller.totalPrice;
    // final double vatAmount = subtotal * (double.parse(vat) / 100);
    // final double totalIncludingVat = subtotal + vatAmount;

    return Container(
      width: 302,
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Logo
            if (logoUrl.isNotEmpty)
              Center(
                child: Image.network(
                  logoUrl,
                  width: 200,
                  height: 100,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) =>
                      const SizedBox(),
                ),
              ),

            // if (logoUrl.isNotEmpty) const SizedBox(height: 10),
            // const SizedBox(height: 25),

            // Restaurant Name and Invoice Title
            InvoiceTitle(
              restaurantName: restaurntName,
              fontSize: 12,
            ),

            // Invoice Number
            InvoiceNumberBox(
              invoiceNumber: invoiceNumber,
              fontSize: 20,
            ),
            const SizedBox(height: 12),

            // Company Info Table
            CompanyInfoTable(
              vatNumber: vatNumber,
              commerce: commerce,
              mainAddress: mainAddress,
              cashierName: cashierName,
              fontSize: 10,
            ),

            Divider(
              color: AppColors.black,
            ),

            // Column Headers
            InvoiceColumnHeaders(fontSize: 12),
            Divider(
              color: AppColors.black,
            ),

            // Order Items
            Obx(() {
              final items = controller
                      .orderDetailsController.orderDetailsModel.data?.items ??
                  [];
              if (controller.orderDetailsController.loading.isTrue) {
                return const Center(child: CircularProgressIndicator());
              }
              return Column(
                children: items
                    .map((item) => InvoiceItemRow(
                          quantity: item.qty ?? '0',
                          name: item.productName ?? '',
                          price: '${item.totalPrice ?? '0.00'} ${'SR'.tr}',
                          fontSize: 12,
                        ))
                    .toList(),
              );
            }),

            Divider(
              color: AppColors.black,
            ),

            // Totals - Calculate based on addTax setting
            Obx(() {
              final vatPercentage = double.parse(vat);
              final totalPrice = controller.totalPrice;
              // Force observation of the addTax value
              final addTax = controller.settingsController.addTax.value;

              if (addTax) {
                // When addTax is true: ADD VAT to the base price
                final basePrice = totalPrice;
                final vatAmount = basePrice * (vatPercentage / 100);
                final totalIncludingVat = basePrice + vatAmount;

                return Column(
                  children: [
                    InvoiceTotalRow(
                      label: 'المجموع بدون الضريبة - Subtotal',
                      value: '${basePrice.toStringAsFixed(2)} SR',
                      labelFontSize: 10,
                      valueFontSize: 12,
                    ),
                    InvoiceTotalRow(
                      label: '%($vat) الضريبة - VAT',
                      value: '${vatAmount.toStringAsFixed(2)} SR',
                      labelFontSize: 10,
                      valueFontSize: 12,
                    ),
                    InvoiceTotalRow(
                      label: 'الإجمالي شامل الضريبة - Total including VAT',
                      value: '${totalIncludingVat.toStringAsFixed(2)} SR',
                      labelFontSize: 10,
                      valueFontSize: 14,
                    ),
                  ],
                );
              } else {
                // Second equation: Price INCLUDES VAT
                final sellingPrice = totalPrice;
                final vatAmount =
                    (sellingPrice * vatPercentage) / (100 + vatPercentage);
                final netAmount = sellingPrice - vatAmount;

                return Column(
                  children: [
                    InvoiceTotalRow(
                      label: 'صافي المبلغ - Net Amount',
                      value: '${netAmount.toStringAsFixed(2)} SR',
                      labelFontSize: 10,
                      valueFontSize: 12,
                    ),
                    InvoiceTotalRow(
                      label: '%($vat) الضريبة - VAT',
                      value: '${vatAmount.toStringAsFixed(2)} SR',
                      labelFontSize: 10,
                      valueFontSize: 12,
                    ),
                    InvoiceTotalRow(
                      label: 'مبلغ البيع - Selling Price',
                      value: '${sellingPrice.toStringAsFixed(2)} SR',
                      labelFontSize: 10,
                      valueFontSize: 14,
                    ),
                  ],
                );
              }
            }),
            Divider(
              color: AppColors.black,
            ),
            // Payment Methods
            Obx(
              () => controller.orderDetailsController.loading.isTrue
                  ? const Center(child: CircularProgressIndicator())
                  : ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: controller
                              .orderDetailsController
                              .orderDetailsModel
                              .data
                              ?.paymentTransactions
                              ?.length ??
                          0,
                      itemBuilder: (context, index) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${controller.orderDetailsController.orderDetailsModel.data?.paymentTransactions?[index].paymentMethod?.description?[1].name ?? ''} ${controller.orderDetailsController.orderDetailsModel.data?.paymentTransactions?[index].paymentMethod?.description?[0].name ?? ''}',
                              style: TextStyle(fontSize: 14),
                            ),
                            Text(
                              '${controller.orderDetailsController.orderDetailsModel.data?.paymentTransactions?[index].amount ?? ''} ${'SR'.tr}',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 14),
                            ),
                          ],
                        );
                      },
                    ),
            ),

            const SizedBox(height: 6),

            // QR Code
            if (websiteUrl.isNotEmpty)
              Center(
                child: QrImageView(
                  data: websiteUrl,
                  version: QrVersions.auto,
                  size: 150,
                ),
              ),

            const SizedBox(height: 3),

            // Footer
            Center(
              child: Text(
                'Powered by $websiteUrl',
                style: const TextStyle(fontSize: 10),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
