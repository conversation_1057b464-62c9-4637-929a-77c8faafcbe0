import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';

class OrderFooter extends StatelessWidget {
  final String? customerName;
  final String? netAmount;

  const OrderFooter({
    super.key,
    required this.customerName,
    required this.netAmount,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: AppSize.height(43),
      decoration: BoxDecoration(
        color: AppColors.tablesBackground,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: AppSize.height(6),
          horizontal: AppSize.width(10),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              customerName?.toString() ?? '',
              style: AppTextStyle.darkGray11600,
            ),
            Text(
              '${netAmount ?? ''} ${'SR'.tr}',
              style: AppTextStyle.darkGray11600,
            ),
          ],
        ),
      ),
    );
  }
}
