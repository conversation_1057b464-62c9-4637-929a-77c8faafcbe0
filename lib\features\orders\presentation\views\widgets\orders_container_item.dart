import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/orders/presentation/views/widgets/order_pop_up_menu.dart';
import 'package:point_of_sale/features/orders/presentation/views/widgets/orders_item_list_view.dart';
import 'package:point_of_sale/features/orders/presentation/views/widgets/orders_item_status_container.dart';

import '../../../../../core/routes/app_pages.dart';
import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../../../../core/utils/size_utils.dart';
import '../../getx/controllers/orders_controller.dart';

class OrdersContainerItem extends GetView<OrdersController> {
  const OrdersContainerItem({super.key, required this.index});
  final int index;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.lavenderGray,
        ),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Stack(
        children: [
          Align(
            alignment: AlignmentDirectional.topStart,
            child: Container(
              height: AppSize.height(31),
              width: AppSize.width(77),
              decoration: BoxDecoration(
                borderRadius: BorderRadiusDirectional.only(
                  topStart: Radius.circular(10),
                  bottomEnd: Radius.circular(10),
                ),
                color: AppColors.primaryWithOpacity3,
              ),
              child: Center(
                child: Text(
                  controller.getOrderAtIndex(index)?.typeId == 0
                      ? 'takeAway'.tr
                      : 'dineIn'.tr,
                  style: AppTextStyle.primary12800,
                ),
              ),
            ),
          ),
          OrderPopupMenu(
            index: index,
          ),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  '#${controller.getOrderAtIndex(index)?.orderNo ?? ''}',
                  style: AppTextStyle.darkgreen20800,
                ),
                OrdersItemsListView(index: index),
                Container(
                  height: AppSize.height(43),
                  decoration: BoxDecoration(
                    color: AppColors.tablesBackground,
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: AppSize.height(6),
                      horizontal: AppSize.width(10),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          controller
                                  .getOrderAtIndex(index)
                                  ?.customerName
                                  ?.toString() ??
                              '',
                          style: AppTextStyle.darkGray11600,
                        ),
                        Text(
                          '${controller.getOrderAtIndex(index)?.netAmount ?? ''} ${'SR'.tr}',
                          style: AppTextStyle.darkGray11600,
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: AppSize.height(8),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: () {
                        Get.toNamed(Routes.print, arguments: {
                          'orderId': controller.getOrderAtIndex(index)?.id,
                          'orderNumber':
                              controller.getOrderAtIndex(index)?.orderNo,
                          'totalPrice': num.parse(
                              controller.getOrderAtIndex(index)?.totalAmount),
                        });
                      },
                      child: Container(
                        height: AppSize.height(31),
                        width: AppSize.width(65),
                        margin: EdgeInsetsDirectional.only(
                          start: AppSize.width(10),
                          bottom: AppSize.height(10),
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppColors.lavenderGray,
                          ),
                          borderRadius: BorderRadiusDirectional.circular(6),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.print_outlined,
                            color: AppColors.primaryColor,
                            size: getSize(24),
                          ),
                        ),
                      ),
                    ),
                    OrdersItemStatusContainer(
                      index: index,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
