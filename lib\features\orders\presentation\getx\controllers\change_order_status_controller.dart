import 'package:get/get.dart';
import 'package:point_of_sale/features/orders/data/models/change_order_status_model.dart';

import '../../../../../core/errors/exceptions.dart';
import '../../../../../core/errors/failure.dart';
import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/widgets/failed_snack_bar.dart';
import '../../../domain/entitis/change_order_status_entity.dart';
import '../../../domain/use_case/change_order_status_use_case.dart';

class ChangeOrderStatusController extends GetxController {
  final ChangeOrderStatusUseCase changeOrderStatusUseCase;
  final CashDataSource cashDataSource = Get.put(CashDataSource());
  ChangeOrderStatusModel changeOrderStatusModel = ChangeOrderStatusModel();
  final loading = true.obs;
  ChangeOrderStatusController(this.changeOrderStatusUseCase);
  Future<void> changeOrderStatus(String orderId, String newStatusId) async {
    final result = await changeOrderStatusUseCase(
      ChangeOrderStatusEntity(
        loading: loading,
        tenantId: cashDataSource.box.read('tenantId'),
        companyId: cashDataSource.box.read('companyId'),
        branchId: cashDataSource.box.read('branchId'),
        orderId: orderId,
        newStatusId: newStatusId,
      ),
    );
    result.fold(
      (failure) {
        String errorMessage;
        if (failure is ServerFailure) {
          errorMessage = failure.message;
        } else if (failure is AppExceptions) {
          errorMessage = failure.message;
        } else {
          errorMessage = 'somethingWentWrongPleaseTryAgainLater'.tr;
        }
        failedSnaskBar(errorMessage);
      },
      (data) {
        changeOrderStatusModel = data;
      },
    );
  }
}
