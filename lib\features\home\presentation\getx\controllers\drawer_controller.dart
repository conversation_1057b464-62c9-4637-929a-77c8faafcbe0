import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/routes/app_pages.dart';
import 'package:point_of_sale/core/services/cash_data_source.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/close_session_info_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/get_cash_in_out_controller.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../../../../core/widgets/success_snack_bar.dart';
import '../../../../settings/presentation/getx/controllers/settings_controller.dart';
import '../../views/widgets/close_session_dialog_body.dart';

/// A dedicated controller for the drawer to reduce dependencies
class AppDrawerController extends GetxController {
  final GetCashInOutController getCashInOutController =
      Get.find<GetCashInOutController>();
  final CashDataSource cashDataSource = Get.put(CashDataSource());
  final CloseSessionInfoController closeSessionInfoController =
      Get.find<CloseSessionInfoController>();
  final SettingsController settingsController = Get.find<SettingsController>();

  void navigateToHome() {
    Get.offNamed(Routes.home);
  }

  void navigateToTables() {
    Get.offNamed(Routes.tables);
  }

  void navigateToSettings() {
    Get.toNamed(Routes.settings);
  }

  void navigateToOrders() {
    Get.offNamed(Routes.orders);
  }

  void navigateToKitchenOrders() {
    Get.offNamed(Routes.kitchenOrders);
  }

  void logout() {
    Get.offAllNamed(Routes.login);
    cashDataSource.logout();
    successSnackBar('logoutSuccessfully'.tr);
  }

  void prepareCloseSession(BuildContext context) {
    closeSessionInfoController.closeSessionInfo();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.white,
          title: Center(
            child: Text(
              'closeSession'.tr,
              style: AppTextStyle.primary20800,
            ),
          ),
          contentPadding: EdgeInsets.symmetric(
            horizontal: AppSize.width(25),
            vertical: AppSize.height(25),
          ),
          scrollable: true,
          content: const CloseSessionDialogBody(),
        );
      },
    );
  }
}
