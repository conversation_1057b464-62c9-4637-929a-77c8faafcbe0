import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';

import '../../../../../core/utils/app_assets.dart';
import '../../../../../core/utils/size_config.dart';

class PrintersList extends GetView<PrintersIPAddressController> {
  const PrintersList({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.printers.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.print_disabled_outlined,
                size: AppSize.height(64),
                color: AppColors.grey,
              ),
              SizedBox(height: AppSize.height(16)),
              Text(
                'noPrintersConfigured'.tr,
                style: AppTextStyle.primary16800.copyWith(
                  color: AppColors.grey,
                ),
              ),
              SizedBox(height: AppSize.height(8)),
              Text(
                'addAPrinterIPAddressAboveToGetStarted'.tr,
                style: AppTextStyle.primary12700.copyWith(
                  color: AppColors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return Expanded(
        child: ListView.builder(
          shrinkWrap: true,
          // physics: const NeverScrollableScrollPhysics(),
          itemCount: controller.printers.length,
          itemBuilder: (context, index) {
            final printer = controller.printers[index];
            return Container(
              margin: EdgeInsets.only(bottom: AppSize.height(8)),
              padding: EdgeInsets.all(AppSize.width(12)),
              decoration: BoxDecoration(
                color: AppColors.white,
                border: Border.all(
                  color: AppColors.lavenderGray,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.print_outlined,
                    color: AppColors.primaryColor,
                    size: AppSize.height(24),
                  ),
                  SizedBox(width: AppSize.width(12)),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          printer.type.translatedDisplayName,
                          style: AppTextStyle.primary14700.copyWith(
                            color: AppColors.primaryColor,
                          ),
                        ),
                        SizedBox(height: AppSize.height(2)),
                        Text(
                          printer.ip,
                          style: AppTextStyle.primary16800,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: AppSize.width(12)),
                  InkWell(
                    onTap: () async {
                      await controller.testPrinterConnection(printer.ip);
                    },
                    child: Container(
                      width: AppSize.width(75),
                      height: AppSize.height(27),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: AppColors.green,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            AppAssets.printerTestIcon,
                            height: AppSize.height(15),
                          ),
                          SizedBox(width: AppSize.width(4)),
                          Text(
                            'test'.tr,
                            style: AppTextStyle.green14600.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(width: AppSize.width(12)),
                  InkWell(
                    onTap: () {
                      controller.removePrinter(printer);
                    },
                    child: Container(
                      width: AppSize.width(75),
                      height: AppSize.height(27),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: AppColors.red,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.delete_outlined,
                            color: AppColors.red,
                            size: AppSize.height(20),
                          ),
                          SizedBox(width: AppSize.width(4)),
                          Text(
                            'delete'.tr,
                            style: AppTextStyle.red14700,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      );
    });
  }
}
