import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/kitchen_orders_controller.dart';

class OrderHeader extends GetView<KitchenOrdersController> {
  final String? orderNo;
  final int index;

  const OrderHeader({
    super.key,
    required this.orderNo,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          height: AppSize.height(16),
        ),
        Text(
          '#${orderNo ?? ''}',
          style: AppTextStyle.darkgreen20800,
        ),
        Padding(
          padding: EdgeInsetsGeometry.only(
            top: AppSize.height(10),
            left: AppSize.width(10),
            right: AppSize.width(10),
            bottom: AppSize.height(18),
          ),
          child: <PERSON><PERSON><PERSON><PERSON>(
            height: AppSize.height(91),
            child: ListView.separated(
              itemCount:
                  controller.kitchenOrdersModel.data?[index].items?.length ?? 0,
              separatorBuilder: (BuildContext context, int itemsIndex) {
                return Divider(
                  height: AppSize.height(7),
                  color: AppColors.lavenderGray2,
                );
              },
              itemBuilder: (BuildContext context, int itemsIndex) {
                return Row(
                  children: [
                    Text(
                      controller.kitchenOrdersModel.data?[index]
                              .items?[itemsIndex].productName ??
                          '',
                      style: AppTextStyle.primary12800,
                    ),
                    Spacer(),
                    Text(
                      'X',
                      style: AppTextStyle.primary12700,
                    ),
                    SizedBox(
                      width: AppSize.width(3),
                    ),
                    Container(
                      height: AppSize.height(12),
                      width: AppSize.height(12),
                      decoration: BoxDecoration(
                        color: AppColors.third,
                      ),
                      child: Center(
                        child: Text(
                          controller.kitchenOrdersModel.data?[index]
                                  .items?[itemsIndex].qty ??
                              '',
                          style: AppTextStyle.primary10700,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
