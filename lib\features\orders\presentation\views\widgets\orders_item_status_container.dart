import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/orders_controller.dart';

class OrdersItemStatusContainer extends GetView<OrdersController> {
  const OrdersItemStatusContainer({
    required this.index,
    super.key,
  });
  final int index;
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: AlignmentDirectional.bottomEnd,
      child: Container(
        height: AppSize.height(31),
        width: AppSize.width(77),
        decoration: BoxDecoration(
          color: controller.getOrderAtIndex(index)?.statusId == 1
              ? AppColors.babyBlue
              : controller.getOrderAtIndex(index)?.statusId == 2
                  ? AppColors.lightYellow
                  : controller.getOrderAtIndex(index)?.statusId == 3
                      ? AppColors.lightGreen
                      : controller.getOrderAtIndex(index)?.statusId == 4
                          ? AppColors.lightPink
                          : controller.getOrderAtIndex(index)?.statusId == 5
                              ? AppColors.grey2
                              : AppColors.primaryColor,
          borderRadius: BorderRadiusDirectional.only(
            topStart: Radius.circular(10),
            bottomEnd: Radius.circular(10),
          ),
        ),
        child: Center(
          child: Text(
            controller.getOrderAtIndex(index)?.statusId == 1
                ? 'new'.tr
                : controller.getOrderAtIndex(index)?.statusId == 2
                    ? 'inProgress'.tr
                    : controller.getOrderAtIndex(index)?.statusId == 3
                        ? 'completed'.tr
                        : controller.getOrderAtIndex(index)?.statusId == 4
                            ? 'pending'.tr
                            : controller.getOrderAtIndex(index)?.statusId == 5
                                ? 'cancel'.tr
                                : '',
            style: AppTextStyle.darkGray12800,
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}
