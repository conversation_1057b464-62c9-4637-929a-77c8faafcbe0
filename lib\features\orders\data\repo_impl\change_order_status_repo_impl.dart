import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failure.dart';
import '../../domain/entitis/change_order_status_entity.dart';
import '../../domain/repo/change_order_status_repo.dart';
import '../models/change_order_status_model.dart';
import '../remote/change_order_status_remote_data_source.dart';

class ChangeOrderStatusRepoImpl implements ChangeOrderStatusRepo {
  final ChangeOrderStatusRemoteDataSource changeOrderStatusRemoteDataSource;

  ChangeOrderStatusRepoImpl(this.changeOrderStatusRemoteDataSource);
  @override
  Future<Either<Failure, ChangeOrderStatusModel>> changeOrderStatus(
      ChangeOrderStatusEntity changeOrderStatusEntity) async {
    try {
      final result = await changeOrderStatusRemoteDataSource
          .changeOrderStatus(changeOrderStatusEntity);
      return Right(result);
    } on AppExceptions catch (e) {
      return Left(ServerFailure(e.message));
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure('generalError'.tr));
    }
  }
}
