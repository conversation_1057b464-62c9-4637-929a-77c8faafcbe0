import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../getx/controllers/categories_controller.dart';
import 'food_item_widget.dart';

class FoodItemAction extends GetView<CategoriesController> {
  const FoodItemAction({
    super.key,
    required this.productId,
    required this.productName,
    required this.price,
    required this.product,
    required this.image,
    required this.totalPrice,
  });

  final String product, image, productId, productName, price, totalPrice;

  // Recalculate item total price including discount
  void _recalculateItemTotal(Map<String, String> item) {
    final qty = int.tryParse(item['qty'] ?? '1') ?? 1;
    final unitPrice = double.tryParse(item['price'] ?? '0') ?? 0;
    final discountPercent = double.tryParse(item['discount'] ?? '0') ?? 0;

    // Calculate subtotal before discount
    final subtotal = unitPrice * qty;

    // Calculate discount amount
    final discountAmount = subtotal * (discountPercent / 100);

    // Calculate final total after discount
    final finalTotal = subtotal - discountAmount;

    // Update the item with calculated values
    item['subtotal'] = subtotal.toStringAsFixed(2);
    item['discountAmount'] = discountAmount.toStringAsFixed(2);
    item['totalPrice'] = finalTotal.toStringAsFixed(2);
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        bool itemFound = false;

        for (var i = 0; i < controller.orderController.localCart.length; i++) {
          var item = controller.orderController.localCart[i];
          if (item['productId'] == productId) {
            int currentQty = int.tryParse(item['qty'] ?? '1') ?? 1;
            currentQty++;
            // Update the quantity and recalculate totals
            item['qty'] = currentQty.toString();
            _recalculateItemTotal(item);
            controller.orderController.localCart[i] = item;
            itemFound = true;
            break;
          }
        }

        if (!itemFound) {
          final unitPrice = double.tryParse(price) ?? 0;
          final foodItem = {
            'productId': productId,
            'productName': productName,
            'price': price,
            'originalPrice':
                price, // Store original price for reset functionality
            'totalPrice': unitPrice.toStringAsFixed(
                2), // Will be recalculated if discount is applied
            'product': product,
            'image': image,
            'qty': '1',
            'discount': '0', // Default discount percentage
            'discountAmount': '0', // Calculated discount amount
            'subtotal':
                unitPrice.toStringAsFixed(2), // Subtotal before discount
          };
          controller.orderController.localCart.add(foodItem);
        }

        // Refresh the RxList to notify GetX of the changes.
        controller.orderController.localCart.refresh();
        // controller.orderController.selectedQuantity.value = 1;
        // controller.ordersListController.getOrdersList();
      },
      child: FoodItemWidget(image: image, product: product),
    );
  }
}
