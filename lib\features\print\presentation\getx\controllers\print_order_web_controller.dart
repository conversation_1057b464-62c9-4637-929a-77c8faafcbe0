import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/services.dart'
    show rootBundle; // لاستيراد الخطّ من assets
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:point_of_sale/core/services/cash_data_source.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_details_controller.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:printing/printing.dart';
import 'package:qr_flutter/qr_flutter.dart';

class PrintOrderWebController extends GetxController {
  /// 1) دالة لتحميل الخطّ العربي من ملف في assets
  Future<pw.Font> _loadArabicFont() async {
    final fontData = await rootBundle.load("assets/fonts/Cairo-SemiBold.ttf");
    return pw.Font.ttf(fontData);
  }

  /// 2) Fetches the logo bytes (via cache) or throws if something goes wrong.
  Future<Uint8List> _fetchLogoBytes(String url) async {
    try {
      // 1. Use DefaultCacheManager to download (or retrieve from cache) the file.
      final File file = await DefaultCacheManager().getSingleFile(url);
      // 2. Read raw bytes
      final Uint8List bytes = await file.readAsBytes();
      return bytes;
    } catch (err) {
      throw Exception('Failed to load logo from network: $err');
    }
  }

  final OrderController orderController = Get.find<OrderController>();
  Future<void> printInvoice(PrintController controller) async {
    // Get cached invoice data
    final cashDataSource = Get.find<CashDataSource>();
    final invoiceData = cashDataSource.getInvoiceData();

    final logoUrl = invoiceData['logo'] as String;
    final commerce = invoiceData['commerce'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final restaurntName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final vat = invoiceData['vat'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;

    final invoiceNumber = controller.orderNumber.toString();
    final double subtotal = double.parse(controller.totalPrice.toString());
    final double vatAmount = subtotal * (double.parse(vat) / 100);
    final double totalIncludingVat = subtotal + vatAmount;

    try {
      // 1) Resolve order details controller
      final orderDetailsController = Get.find<OrderDetailsController>();

      // 2) Create a new PDF document
      final pdf = pw.Document();

      // 3) حمّل الخطّ العربي (Cairo) لمرة واحدة
      final arabicFont = await _loadArabicFont();

      // 4) Pre‐fetch الصورة الشبكية (logo) إذا كان الرابط غير فارغ
      Uint8List? logoBytes;
      if (logoUrl.isNotEmpty) {
        try {
          logoBytes = await _fetchLogoBytes(logoUrl);
        } catch (_) {
          logoBytes = null; // إذا فشل التحميل، سنتخطّى الشعار
        }
      }

      // 5) Generate QR code data (binary) لاستخدامه كـ MemoryImage
      final qrData = await QrPainter(
        data: websiteUrl,
        version: QrVersions.auto,
        errorCorrectionLevel: QrErrorCorrectLevel.M,
      ).toImageData(500);

      // 6) أضف صفحة واحدة بصيغة رول-80
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.roll80,
          // margin: pw.EdgeInsets.all(10),
          build: (pw.Context context) {
            return pw.Directionality(
              textDirection: pw.TextDirection.rtl,
              child: pw.ListView(
                children: [
                  // ——— الشعار (logo) ———
                  if (logoBytes != null)
                    pw.Center(
                      child: pw.Image(
                        pw.MemoryImage(logoBytes),
                        width: 200,
                        height: 150,
                        fit: pw.BoxFit.contain,
                      ),
                    ),

                  if (logoBytes != null) pw.SizedBox(height: 10),
                  pw.SizedBox(height: 25),

                  // ——— اسم المطعم والعنوان الرئيسي للفاتورة ———
                  pw.Text(
                    restaurntName,
                    textAlign: pw.TextAlign.center,
                    style: pw.TextStyle(
                      font: arabicFont, // استخدام الخط العربي
                      fontSize: 8,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.Text(
                    'فاتورة ضريبية مبسطة',
                    textAlign: pw.TextAlign.center,
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 8,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.Text(
                    'Simplified Tax Invoice',
                    textAlign: pw.TextAlign.center,
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 8,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),

                  // رقم الفاتورة

                  pw.Center(
                    child: pw.Text(
                      'Order #${invoiceNumber.trim()}',
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 8),

                  // ——— بيانات VAT و C.R و POS و الكاشير و التاريخ ———
                  pw.Table(
                    defaultVerticalAlignment:
                        pw.TableCellVerticalAlignment.middle,
                    columnWidths: {
                      0: pw.FlexColumnWidth(2), // عمود الإنجليزي
                      1: pw.FlexColumnWidth(
                          4), // عمود القيمة (الذي سيكون في الوسط)
                      2: pw.FlexColumnWidth(3), // عمود العربي
                    },
                    children: [
                      // صف 1
                      pw.TableRow(
                        children: [
                          // عمود الإنجليزي (LTR + padding أفقي)
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(
                                vertical: 2, horizontal: 4),
                            child: pw.Text(
                              'VAT',
                              textDirection: pw.TextDirection.ltr,
                              textAlign: pw.TextAlign.left,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                              ),
                            ),
                          ),
                          // عمود القيمة (متوسط—تمت مراعاة توسيط النص)
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(vertical: 2),
                            child: pw.Text(
                              vatNumber,
                              textAlign: pw.TextAlign.center,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                          // عمود العربي (محاذاة افتراضية لليمين)
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(vertical: 2),
                            child: pw.Text(
                              'الرقم الضريبي',
                              textAlign: pw.TextAlign.right,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),

                      // صف 2
                      pw.TableRow(
                        children: [
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(
                                vertical: 2, horizontal: 4),
                            child: pw.Text(
                              'C.R',
                              textDirection: pw.TextDirection.ltr,
                              textAlign: pw.TextAlign.left,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                              ),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(vertical: 2),
                            child: pw.Text(
                              commerce,
                              textAlign: pw.TextAlign.center,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(vertical: 2),
                            child: pw.Text(
                              'السجل التجاري',
                              textAlign: pw.TextAlign.right,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),

                      // صف 3
                      pw.TableRow(
                        children: [
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(
                                vertical: 2, horizontal: 4),
                            child: pw.Text(
                              'POS',
                              textDirection: pw.TextDirection.ltr,
                              textAlign: pw.TextAlign.left,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                              ),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(vertical: 2),
                            child: pw.Text(
                              mainAddress,
                              textAlign: pw.TextAlign.center,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 6,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(vertical: 2),
                            child: pw.Text(
                              'نقطة البيع',
                              textAlign: pw.TextAlign.right,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),

                      // صف 4
                      pw.TableRow(
                        children: [
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(
                                vertical: 2, horizontal: 4),
                            child: pw.Text(
                              'Cashier',
                              textDirection: pw.TextDirection.ltr,
                              textAlign: pw.TextAlign.left,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                              ),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(vertical: 2),
                            child: pw.Text(
                              cashierName,
                              textAlign: pw.TextAlign.center,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(vertical: 2),
                            child: pw.Text(
                              'الكاشير',
                              textAlign: pw.TextAlign.right,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),

                      // صف 5
                      pw.TableRow(
                        children: [
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(
                                vertical: 2, horizontal: 4),
                            child: pw.Text(
                              'Date',
                              textDirection: pw.TextDirection.ltr,
                              textAlign: pw.TextAlign.left,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                              ),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(vertical: 2),
                            child: pw.Text(
                              DateFormat('yyyy/MM/dd hh:mm a')
                                  .format(DateTime.now()),
                              textAlign: pw.TextAlign.center,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.symmetric(vertical: 2),
                            child: pw.Text(
                              'التاريخ',
                              textAlign: pw.TextAlign.right,
                              style: pw.TextStyle(
                                font: arabicFont,
                                fontSize: 8,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  pw.Divider(),

                  // ——— رؤوس عمود البنود ———
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        children: [
                          pw.Text(
                            'Qty',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 10,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.SizedBox(height: 2),
                          pw.Text(
                            'الكمية',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      pw.Column(
                        children: [
                          pw.Text(
                            'Item',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 10,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.SizedBox(height: 2),
                          pw.Text(
                            'الصنف',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      pw.Column(
                        children: [
                          pw.Text(
                            'Price',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 10,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.SizedBox(height: 2),
                          pw.Text(
                            'السعر',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  pw.Divider(),

                  // ——— كل سطر من البنود ———
                  ...?orderDetailsController.orderDetailsModel.data?.items?.map(
                    (item) => pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          item.qty ?? '0',
                          style: pw.TextStyle(
                            font: arabicFont,
                            fontSize: 10,
                          ),
                        ),
                        pw.Text(
                          item.productName ?? '',
                          style: pw.TextStyle(
                            font: arabicFont,
                            fontSize: 10,
                          ),
                        ),
                        pw.Text(
                          '${item.totalPrice ?? '0.00'} ${'SR'.tr}',
                          style: pw.TextStyle(
                            font: arabicFont,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),

                  pw.Divider(thickness: 1),

                  // ——— صف الإجمالي ———
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'المجموع بدون الضريبة - Subtotal',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 6,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        '${controller.totalPrice} SR',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 8,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        '%($vat) الضريبة - VAT',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 6,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        '${controller.totalPrice * int.parse(vat) / 100} SR',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 8,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'الإجمالي شامل الضريبة - Total including VAT',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 6,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        '$totalIncludingVat SR',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 10,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  pw.Divider(),
                  pw.SizedBox(height: 3),
                  // ——— طريقة الدفع و المبلغ المدفوع ———
                  pw.ListView.separated(
                    // shrinkWrap: true,
                    itemCount: controller
                            .orderDetailsController
                            .orderDetailsModel
                            .data
                            ?.paymentTransactions
                            ?.length ??
                        0,
                    separatorBuilder: (context, index) =>
                        pw.SizedBox(height: 8),
                    itemBuilder: (context, index) {
                      return pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            textAlign: pw.TextAlign.right,
                            '${controller.orderDetailsController.orderDetailsModel.data?.paymentTransactions?[index].paymentMethod?.description?[1].name ?? ''} ${controller.orderDetailsController.orderDetailsModel.data?.paymentTransactions?[index].paymentMethod?.description?[0].name ?? ''}',
                            style: pw.TextStyle(
                              fontSize: 8,
                              font: arabicFont,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Align(
                            alignment: pw.Alignment.centerRight,
                            child: pw.Text(
                              '${controller.orderDetailsController.orderDetailsModel.data?.paymentTransactions?[index].amount ?? ''} ${'SR'.tr}',
                              style: pw.TextStyle(
                                fontSize: 8,
                                font: arabicFont,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  // ——— كود الاستجابة السريعة (QR) ———
                  pw.SizedBox(height: 15),
                  pw.Center(
                    child: pw.Image(
                        pw.MemoryImage(
                          qrData?.buffer.asUint8List() ?? Uint8List(0),
                        ),
                        width: 150,
                        height: 150,
                        fit: pw.BoxFit.cover),
                  ),

                  pw.SizedBox(height: 5),

                  // ——— نص في الفوتر ———
                  pw.Center(
                    child: pw.Text(
                      'Powerd by $websiteUrl',
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      );

      // 7) أرسل مستند الـ PDF للطابعة أو المعاينة
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
