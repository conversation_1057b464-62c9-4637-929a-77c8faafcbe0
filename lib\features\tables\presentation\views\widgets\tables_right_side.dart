import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/tables/presentation/views/widgets/tables_container.dart';
import 'package:point_of_sale/features/tables/presentation/views/widgets/tables_places.dart';
import 'package:point_of_sale/features/tables/presentation/views/widgets/tables_status.dart';

import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/tables_controller.dart';

class TablesRightSide extends GetView<TablesController> {
  const TablesRightSide({
    required this.margin,
    super.key,
  });
  final EdgeInsetsGeometry margin;
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        margin: margin,
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  'manageTables'.tr,
                  style: AppTextStyle.primary20800,
                ),
                const Spacer(),
                const TablesPlaces(),
              ],
            ),
            const TablesStatus(),
            TablesContainer(
              margin: EdgeInsetsDirectional.only(
                top: AppSize.height(16),
                bottom: AppSize.height(23),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
