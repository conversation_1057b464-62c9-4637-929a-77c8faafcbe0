import 'package:get/get.dart';

import '../../../injection_controller.dart';
import '../controllers/lock_screen_controller.dart';
import '../controllers/open_lock_sceen_controller.dart';

class LockScreenBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<LockScreenController>(
      () => LockScreenController(
        InjectionController().getIt(),
      ),
    );
    Get.lazyPut<OpenLockScreenController>(
      () => OpenLockScreenController(),
    );
    // Get.lazyPut<LockScreenTimerController>(
    //   () => LockScreenTimerController(),
    // );
  }
}
