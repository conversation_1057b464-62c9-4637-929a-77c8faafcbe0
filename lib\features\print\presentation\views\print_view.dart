import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/print/presentation/views/widgets/app_bar_widget.dart';
import 'package:point_of_sale/features/print/presentation/views/widgets/invoice.dart';
import 'package:point_of_sale/features/print/presentation/views/widgets/new_order_button.dart';
import 'package:point_of_sale/features/print/presentation/views/widgets/payment_success_box.dart';
import 'package:point_of_sale/features/print/presentation/views/widgets/print_receipt_button.dart';
import '../../../../core/utils/size_config.dart';
import '../../../home/<USER>/views/widgets/custom_drawer.dart';

class PrintView extends GetView<PrintController> {
  const PrintView({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: PrintAppBar(),
        drawer: Drawer(
          backgroundColor: AppColors.white,
          width: AppSize.width(372),
          child: const CustomDrawer(),
        ),
        body: SafeArea(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Left side: Payment Success + Print Receipt
              Container(
                width: AppSize.width(425),
                color: AppColors.lightGreen,
                padding: EdgeInsetsDirectional.only(
                  top: AppSize.height(41),
                  start: AppSize.width(24),
                  end: AppSize.width(24),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const PaymentSuccessBox(),
                    SizedBox(height: AppSize.height(21)),
                    const PrintReceiptButton(),
                    const Spacer(),
                    NewOrderButton(),
                    SizedBox(height: AppSize.height(42)),
                  ],
                ),
              ),

              // Right side: Invoice
              Expanded(
                child: Container(
                  color: Colors.grey.shade100,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 20,
                  ),
                  child: Center(
                    child: Invoice(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
