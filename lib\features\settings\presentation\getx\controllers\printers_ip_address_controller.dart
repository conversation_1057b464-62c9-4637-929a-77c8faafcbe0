import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/core/widgets/success_snack_bar.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/settings_controller.dart';

// Printer type enum
enum PrinterType {
  customer('customerPrinter'),
  kitchen('kitchenPrinter'),
  sales('salesPrinter');

  const PrinterType(this.displayName);
  final String displayName;

  // Get translated display name
  String get translatedDisplayName => displayName.tr;
}

// Printer model class
class PrinterInfo {
  final String ip;
  final PrinterType type;

  PrinterInfo({required this.ip, required this.type});

  Map<String, dynamic> toJson() => {
        'ip': ip,
        'type': type.name,
      };

  factory PrinterInfo.fromJson(Map<String, dynamic> json) => PrinterInfo(
        ip: json['ip'],
        type: PrinterType.values.firstWhere((e) => e.name == json['type']),
      );
}

/// Controller for managing printer IP addresses and default cashier printers
///
/// Features:
/// - Configure multiple printers with different types (customer, kitchen, sales)
/// - Set multiple default cashier printers for simultaneous printing (includes all printer types)
/// - Cashiers can print to customer, kitchen, and sales printers simultaneously
/// - Print to all selected default cashier printers at once
///
/// Usage for printing to default cashier printers:
/// ```dart
/// final controller = Get.find<PrintersIPAddressController>();
/// if (controller.hasDefaultCashierPrinters) {
///   final results = await controller.printToDefaultCashierPrinters(printData);
/// }
/// ```
class PrintersIPAddressController extends GetxController {
  final GetStorage _storage = GetStorage();
  final TextEditingController ipController = TextEditingController();
  final SettingsController settingsController = Get.find<SettingsController>();
  final FocusNode ipFocusNode = FocusNode();

  // Observable list of printer information
  final RxList<PrinterInfo> printers = <PrinterInfo>[].obs;

  // Selected printer type for dropdown
  final Rx<PrinterType> selectedPrinterType = PrinterType.customer.obs;

  // Key for storing printer info in local storage
  static const String _printersKey = 'printer_info_list';
  static const String _defaultCashierPrintersKey = 'default_cashier_printers';

  // Observable list of default cashier printer IPs
  final RxList<String> defaultCashierPrinters = <String>[].obs;

  // Backward compatibility - observable list of printer IP addresses only
  RxList<String> get printerIPs =>
      printers.map((printer) => printer.ip).toList().obs;

  @override
  void onInit() {
    super.onInit();
    _loadPrinterIPs();
    _loadDefaultCashierPrinters();
  }

  /// Load printer info from local storage
  void _loadPrinterIPs() {
    // Try to load new format first
    final List<dynamic>? storedPrinters = _storage.read(_printersKey);
    if (storedPrinters != null) {
      try {
        printers.assignAll(
          storedPrinters
              .map((json) =>
                  PrinterInfo.fromJson(Map<String, dynamic>.from(json)))
              .toList(),
        );
        return;
      } catch (e) {
        // If new format fails, try old format for backward compatibility
      }
    }

    // Backward compatibility: try to load old format (just IP strings)
    final List<dynamic>? storedIPs = _storage.read('printer_ips');
    if (storedIPs != null) {
      printers.assignAll(
        storedIPs
            .cast<String>()
            .map((ip) => PrinterInfo(ip: ip, type: PrinterType.customer))
            .toList(),
      );
      // Save in new format
      _savePrinterIPs();
    }
  }

  /// Save printer info to local storage
  void _savePrinterIPs() {
    _storage.write(
        _printersKey, printers.map((printer) => printer.toJson()).toList());
  }

  /// Add a new printer with IP and type
  void addPrinter(String ip, PrinterType type) {
    if (ip.isNotEmpty && !printers.any((printer) => printer.ip == ip)) {
      final newPrinter = PrinterInfo(ip: ip, type: type);
      printers.add(newPrinter);
      _savePrinterIPs();
      successSnackBar(
        '${type.translatedDisplayName} added successfully',
      );
    } else if (printers.any((printer) => printer.ip == ip)) {
      failedSnaskBar(
        'This IP address already exists',
      );
    }
  }

  /// Add a new printer IP address (backward compatibility)
  void addPrinterIP(String ip) {
    addPrinter(ip, selectedPrinterType.value);
  }

  /// Remove a printer by IP address
  void removePrinterIP(String ip) {
    printers.removeWhere((printer) => printer.ip == ip);
    _savePrinterIPs();
    successSnackBar(
      'Printer removed successfully',
    );
  }

  /// Remove a specific printer
  void removePrinter(PrinterInfo printer) {
    printers.remove(printer);
    _savePrinterIPs();
    successSnackBar(
      '${printer.type.translatedDisplayName} removed successfully',
    );
  }

  /// Clear all printers
  void clearAllPrinterIPs() {
    printers.clear();
    _savePrinterIPs();
    successSnackBar(
      'All printers cleared',
    );
  }

  /// Set selected printer type
  void setSelectedPrinterType(PrinterType type) {
    selectedPrinterType.value = type;
  }

  /// Validate IP address format
  bool isValidIP(String ip) {
    final RegExp ipRegex = RegExp(
      r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',
    );
    return ipRegex.hasMatch(ip);
  }

  /// Get all printer IPs (backward compatibility)
  List<String> getAllPrinterIPs() {
    return printers.map((printer) => printer.ip).toList();
  }

  /// Get all printers
  List<PrinterInfo> getAllPrinters() {
    return printers.toList();
  }

  /// Get printers by type
  List<PrinterInfo> getPrintersByType(PrinterType type) {
    return printers.where((printer) => printer.type == type).toList();
  }

  /// Get kitchen printer IPs only
  List<String> getKitchenPrinterIPs() {
    return printers
        .where((printer) => printer.type == PrinterType.kitchen)
        .map((printer) => printer.ip)
        .toList();
  }

  /// Check if any printers are configured
  bool get hasPrinters => printers.isNotEmpty;

  /// Load default cashier printers from local storage
  void _loadDefaultCashierPrinters() {
    final List<dynamic>? storedPrinters =
        _storage.read(_defaultCashierPrintersKey);
    if (storedPrinters != null) {
      defaultCashierPrinters.assignAll(storedPrinters.cast<String>());
    }
  }

  /// Save default cashier printers to local storage
  void _saveDefaultCashierPrinters() {
    _storage.write(_defaultCashierPrintersKey, defaultCashierPrinters.toList());
  }

  /// Add a printer IP to default cashier printers
  void addDefaultCashierPrinter(String ip) {
    if (ip.isNotEmpty && !defaultCashierPrinters.contains(ip)) {
      // Check if the printer exists in the configured printers
      if (printers.any((printer) => printer.ip == ip)) {
        defaultCashierPrinters.add(ip);
        _saveDefaultCashierPrinters();
        successSnackBar(
          'Printer added to default cashier printers',
        );
      } else {
        failedSnaskBar(
          'Please configure this printer first before setting it as default',
        );
      }
    } else if (defaultCashierPrinters.contains(ip)) {
      failedSnaskBar(
        'This printer is already in default cashier printers',
      );
    }
  }

  /// Remove a printer IP from default cashier printers
  void removeDefaultCashierPrinter(String ip) {
    if (defaultCashierPrinters.contains(ip)) {
      defaultCashierPrinters.remove(ip);
      _saveDefaultCashierPrinters();
      successSnackBar(
        'Printer removed from default cashier printers',
      );
    }
  }

  /// Toggle a printer in default cashier printers
  void toggleDefaultCashierPrinter(String ip) {
    if (defaultCashierPrinters.contains(ip)) {
      removeDefaultCashierPrinter(ip);
    } else {
      addDefaultCashierPrinter(ip);
    }
  }

  /// Check if a printer is in default cashier printers
  bool isDefaultCashierPrinter(String ip) {
    return defaultCashierPrinters.contains(ip);
  }

  /// Get all available printers for cashier register (includes all printer types)
  /// Cashiers can print to customer, kitchen, and sales printers
  List<PrinterInfo> getAvailableCashierPrinters() {
    return printers.toList();
  }

  /// Clear all default cashier printers
  void clearDefaultCashierPrinters() {
    defaultCashierPrinters.clear();
    _saveDefaultCashierPrinters();
    successSnackBar(
      'All default cashier printers cleared',
    );
  }

  /// Print to all default cashier printers
  Future<List<bool>> printToDefaultCashierPrinters(List<int> printData) async {
    if (defaultCashierPrinters.isEmpty) {
      Get.log('No default cashier printers configured');
      failedSnaskBar('No default cashier printers configured');
      return [];
    }

    final List<Future<bool>> printTasks =
        defaultCashierPrinters.map((ip) async {
      try {
        Get.log('Printing to default cashier printer: $ip');
        return await _printToSpecificPrinter(ip, printData);
      } catch (e) {
        Get.log('Failed to print to cashier printer $ip: $e');
        return false;
      }
    }).toList();

    final results = await Future.wait(printTasks);
    final successCount = results.where((result) => result).length;

    Get.log(
        'Printed to $successCount/${defaultCashierPrinters.length} default cashier printers');

    if (successCount > 0) {
      successSnackBar(
        'Printed to $successCount/${defaultCashierPrinters.length} cashier printers',
      );
    } else {
      failedSnaskBar(
        'Failed to print to all cashier printers',
      );
    }

    return results;
  }

  /// Check if default cashier printers are configured
  bool get hasDefaultCashierPrinters => defaultCashierPrinters.isNotEmpty;

  /// Print to a specific printer IP
  Future<bool> _printToSpecificPrinter(String ip, List<int> printData) async {
    try {
      // Check if running on web
      if (kIsWeb) {
        Get.log('Web printing not supported for actual printing');
        return false;
      }

      // Try multiple ports commonly used by printers
      final List<int> portsToTry = [9100, 515, 631];

      for (int port in portsToTry) {
        try {
          final printer = PrinterNetworkManager(ip, port: port);
          final result = await printer.printTicket(printData).timeout(
                const Duration(seconds: 10),
                onTimeout: () => PosPrintResult.timeout,
              );

          if (result == PosPrintResult.success) {
            Get.log('Successfully printed to $ip:$port');
            return true;
          }
        } catch (e) {
          Get.log('Failed to print to $ip:$port - $e');
          continue;
        }
      }

      Get.log('Failed to print to $ip on all ports');
      return false;
    } catch (e) {
      Get.log('Error printing to $ip: $e');
      return false;
    }
  }

  /// Test connection to a specific printer IP
  Future<bool> testPrinterConnection(String ip) async {
    try {
      Get.log('Testing connection to XPrinter K200L at IP: $ip');

      // Check if running on web
      if (kIsWeb) {
        return await _testWebPrinterConnection(ip);
      }

      // Mobile/Desktop testing
      return await _testMobilePrinterConnection(ip);
    } catch (e) {
      Get.log('Printer test error: $e');
      failedSnaskBar(
        'Printer test failed: $e',
      );
      return false;
    }
  }

  /// Test printer connection on mobile/desktop
  Future<bool> _testMobilePrinterConnection(String ip) async {
    // Try multiple ports commonly used by XPrinter models
    final List<int> portsToTry = [9100, 515, 631, 8080, 80];

    for (int port in portsToTry) {
      try {
        Get.log('Testing mobile connection to $ip:$port');

        final printer = PrinterNetworkManager(ip, port: port);

        // Create a simple test print (just initialize, don't actually print)
        final profile = await CapabilityProfile.load();
        final generator = Generator(PaperSize.mm80, profile);
        final List<int> testBytes = [];
        testBytes.addAll(generator.text('Test Connection'));
        testBytes.addAll(generator.cut());

        // Try to connect with timeout
        final result = await printer.printTicket(testBytes).timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            Get.log('Timeout testing $ip:$port');
            return PosPrintResult.timeout;
          },
        );

        if (result == PosPrintResult.success) {
          Get.log('Successfully connected to $ip:$port');
          successSnackBar(
            'Printer connection test successful on port $port',
          );
          return true;
        }
      } catch (e) {
        Get.log('Connection test failed for $ip:$port - $e');
        continue;
      }
    }

    failedSnaskBar(
      'Failed to connect to printer $ip on all ports',
    );
    return false;
  }

  /// Test printer connection on web using HTTP
  Future<bool> _testWebPrinterConnection(String ip) async {
    final List<int> portsToTry = [9100, 515, 631, 8080, 80];

    for (int port in portsToTry) {
      try {
        Get.log('Testing web connection to $ip:$port');

        // Try HTTP connection test
        final uri = Uri.parse('http://$ip:$port/');
        final response =
            await http.get(uri).timeout(const Duration(seconds: 5));

        // Any response (even error) means the port is reachable
        Get.log(
            'Web connection test to $ip:$port - Status: ${response.statusCode}');
        successSnackBar(
          'Web printer connection test successful on port $port',
        );
        return true;
      } catch (e) {
        Get.log('Web connection test failed for $ip:$port - $e');
        continue;
      }
    }

    failedSnaskBar(
      'Failed to connect to printer $ip on all ports (web)',
    );
    return false;
  }
}
