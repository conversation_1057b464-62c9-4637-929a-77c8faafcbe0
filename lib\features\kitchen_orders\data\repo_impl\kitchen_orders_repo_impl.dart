import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failure.dart';
import '../../domain/entities/kitchen_orders_entity.dart';
import '../../domain/repo/kitchen_orders_repo.dart';
import '../models/kitchen_orders_model.dart';
import '../remote/kitchen_orders_remote_data_source.dart';

class KitchenOrdersRepoImpl implements KitchenOrdersRepo {
  final KitchenOrdersRemoteDataSource kitchenOrdersRemoteDataSource;
  KitchenOrdersRepoImpl(this.kitchenOrdersRemoteDataSource);
  @override
  Future<Either<Failure, KitchenOrdersModel>> getOrders(
      KitchenOrdersEntity kitchenOrdersEntity) async {
    try {
      final result =
          await kitchenOrdersRemoteDataSource.getOrders(kitchenOrdersEntity);
      return Right(result);
    } on AppExceptions catch (e) {
      return Left(ServerFailure(e.message));
    } on DioException catch (e) {
      return Left(ServerFailure(e.message ?? 'Network error occurred'));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: ${e.toString()}'));
    }
  }
}
