import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/session_continuation_controller.dart';

import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/services/session_manager.dart';

class HomeController extends GetxController {
  late final SessionContinuationController sessionContinuationController;
  final CashDataSource cashDataSource = Get.find<CashDataSource>();

  // Static flag to track if dialog has been shown in current app session
  static bool _dialogShownThisSession = false;

  /// Reset the dialog flag (call this when creating a new session)
  static void resetDialogFlag() {
    _dialogShownThisSession = false;
    Get.log('HomeController: Dialog flag reset');
  }

  @override
  void onInit() {
    super.onInit();
    try {
      sessionContinuationController = Get.find<SessionContinuationController>();
    } catch (e) {
      sessionContinuationController = Get.put(SessionContinuationController());
    }
  }

  @override
  void onReady() {
    super.onReady();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final hasActiveSession = SessionManager.hasActiveSession;
      final justCreatedSession =
          cashDataSource.box.read('just_created_session') ?? false;

      Get.log('HomeController: hasActiveSession = $hasActiveSession');
      Get.log('HomeController: justCreatedSession = $justCreatedSession');
      Get.log(
          'HomeController: _dialogShownThisSession = $_dialogShownThisSession');

      // Show dialog only if:
      // 1. There's an active session
      // 2. User didn't just create a session
      // 3. Dialog hasn't been shown in this app session yet
      if (hasActiveSession && !justCreatedSession && !_dialogShownThisSession) {
        Get.log('HomeController: Showing session continuation dialog');
        _dialogShownThisSession = true; // Mark as shown
        final ctx = Get.context;
        if (ctx != null) {
          sessionContinuationController.showSessionContinuationDialog(ctx);
        }
      } else {
        Get.log('HomeController: Not showing dialog');
      }

      // Clear the just created flag after checking
      if (justCreatedSession) {
        cashDataSource.box.remove('just_created_session');
        Get.log('HomeController: Cleared just_created_session flag');
      }
    });
  }
}
