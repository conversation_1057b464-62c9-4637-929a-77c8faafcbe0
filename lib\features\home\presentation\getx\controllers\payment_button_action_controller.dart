import 'package:get/get.dart';
import 'package:point_of_sale/core/routes/app_pages.dart';
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_details_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/orders_list_controller.dart';
import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/widgets/success_snack_bar.dart';
import '../../../../settings/presentation/getx/controllers/settings_controller.dart';

class PaymentButtonActionController extends GetxController {
  final OrderController orderController = Get.find<OrderController>();
  final OrdersListController ordersListController =
      Get.find<OrdersListController>();
  final OrderDetailsController orderDetailsController =
      Get.find<OrderDetailsController>();
  final CashDataSource cashDataSource = Get.put(CashDataSource());
  final SettingsController settingsController = Get.find<SettingsController>();
  Future<void> handlePayment() async {
    final localCart = orderController.localCart;
    final ordersList = ordersListController.ordersListModel.data;
    final currentOrderIndex = ordersListController.order.value;

    // Case 1: If local cart is not empty, fetch order and navigate to payment
    if (localCart.isNotEmpty) {
      List<Future> orderFutures = [];

      for (var item in localCart) {
        final productId = item['productId'] ?? '';
        final productName = item['productName'] ?? '';
        final price = item['price'] ?? '0';
        final qty = int.tryParse(item['qty']!) ?? 1;
        final unitPrice = double.tryParse(price) ?? 0;
        final discountPercent = double.tryParse(item['discount'] ?? '0') ?? 0;
        final subtotal = unitPrice * qty;
        final discountAmount = subtotal * (discountPercent / 100);
        final subtotalAfterDiscount = subtotal - discountAmount;
        final productDisc = discountAmount.toString();
        final productNetTotal = subtotalAfterDiscount;

        // Debug logging for discount calculations
        if (discountPercent > 0) {
          Get.log('PaymentButtonAction - Item: $productName');
          Get.log('PaymentButtonAction - Original subtotal: $subtotal');
          Get.log('PaymentButtonAction - Discount: $discountPercent%');
          Get.log('PaymentButtonAction - Discount amount: $discountAmount');
          Get.log('PaymentButtonAction - Final amount: $subtotalAfterDiscount');
        }
        final vatPercentage = cashDataSource.getInvoiceData()['vat'] as String;
        final productVat = settingsController.addTax.isTrue
            ? (subtotalAfterDiscount * double.parse(vatPercentage) / 100)
                .toString()
            : (subtotalAfterDiscount *
                    double.parse(vatPercentage) /
                    (100 + double.parse(vatPercentage)))
                .toString();
        final productNetTotalWithVat = settingsController.addTax.isTrue
            ? (subtotalAfterDiscount + double.parse(productVat))
            : subtotalAfterDiscount;
        final totalPrice = productNetTotal;
        // If orders list is empty, create new order
        if (ordersList == null || ordersList.isEmpty || currentOrderIndex < 0) {
          failedSnaskBar('ordersListEmpty'.tr);
          return;
        } else {
          // Add to existing order
          orderFutures.add(
            orderController.fetchOrder(
              productId,
              productName,
              qty,
              price,
              totalPrice.toString(),
              ordersList[currentOrderIndex].id ?? 0,
              ordersList[currentOrderIndex].orderNo ?? '',
              productDisc,
              productNetTotal.toString(),
              productVat.toString(),
              productNetTotalWithVat.toString(),
            ),
          );
        }
      }

      try {
        await Future.wait(orderFutures);
        await ordersListController.getOrdersList();

        // Clear local cart after successful order creation
        orderController.localCart.clear();

        final updatedOrdersList = ordersListController.ordersListModel.data;
        if (updatedOrdersList != null && updatedOrdersList.isNotEmpty) {
          final orderIndex = currentOrderIndex >= 0 &&
                  currentOrderIndex < updatedOrdersList.length
              ? currentOrderIndex
              : updatedOrdersList.length - 1;

          if (updatedOrdersList[orderIndex].items != null) {
            for (int i = 0;
                i < updatedOrdersList[orderIndex].items!.length;
                i++) {}
          }

          final serverTotal =
              updatedOrdersList[orderIndex].items?.fold(0.0, (sum, item) {
                    return sum +
                        (double.tryParse(item.totalPrice ?? '0') ?? 0.0);
                  }) ??
                  0.0;
          Get.toNamed(
            Routes.paymentView,
            arguments: {
              'orderId': updatedOrdersList[orderIndex].id,
              'orderNumber': updatedOrdersList[orderIndex].orderNo,
              'totalPrice': serverTotal,
            },
          );
        }
      } catch (error) {
        failedSnaskBar('faildToProcessTheOrder'.tr);
      }
    }
    // Case 2: If local cart is empty
    else {
      // Check if orders list and current order exists
      if (ordersList != null &&
          ordersList.isNotEmpty &&
          currentOrderIndex >= 0 &&
          currentOrderIndex < ordersList.length) {
        final currentOrder = ordersList[currentOrderIndex];

        // Check if order items exist and are not empty
        final hasItems =
            currentOrder.items != null && currentOrder.items!.isNotEmpty;
        final serverTotal = hasItems
            ? currentOrder.items!.fold(0.0, (sum, item) {
                return sum + (double.tryParse(item.totalPrice ?? '0') ?? 0.0);
              })
            : 0.0;

        if (!hasItems || serverTotal == 0) {
          failedSnaskBar('noItemsInTheCart'.tr);
          return;
        }

        // Extract the base price from server total for payment controller
        final invoiceData = cashDataSource.getInvoiceData();
        final addTaxFromCache = invoiceData['addTax'] as bool;
        final vatPercentage = double.parse(invoiceData['vat'] as String);

        final basePriceForPayment = addTaxFromCache && serverTotal > 0
            ? serverTotal /
                (1 +
                    (vatPercentage /
                        100)) // Extract base price from VAT-inclusive total
            : serverTotal; // Use as is if VAT wasn't added

        Get.toNamed(
          Routes.paymentView,
          arguments: {
            'orderId': currentOrder.id,
            'orderNumber': currentOrder.orderNo,
            'totalPrice': basePriceForPayment,
          },
        );
        successSnackBar('itemAddedSuccessfully'.tr);
      } else {
        failedSnaskBar('noItemsInTheCart'.tr);
      }
    }
  }
}
