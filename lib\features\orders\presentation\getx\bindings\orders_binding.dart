import 'package:get/get.dart';
import 'package:point_of_sale/features/orders/presentation/getx/controllers/change_order_status_controller.dart';

import '../../../../../injection_controller.dart';
import '../../../../auth/login/presentation/getx/controllers/login_controller.dart';
import '../../../../home/<USER>/getx/bindings/home_binding.dart';
import '../../../../home/<USER>/getx/controllers/orders_list_controller.dart';
import '../controllers/orders_controller.dart';

class OrdersBinding extends Bindings {
  @override
  void dependencies() {
    HomeBinding().dependencies();
    Get.lazyPut<OrdersController>(
      () => OrdersController(
        InjectionController().getIt(),
      ),
    );
    Get.lazyPut<LoginController>(
      () => LoginController(
        InjectionController().getIt(),
      ),
    );
    Get.lazyPut<OrdersListController>(
      () => OrdersListController(
        InjectionController().getIt(),
      ),
    );
    Get.lazyPut<ChangeOrderStatusController>(
      () => ChangeOrderStatusController(
        InjectionController().getIt(),
      ),
    );
  }
}
