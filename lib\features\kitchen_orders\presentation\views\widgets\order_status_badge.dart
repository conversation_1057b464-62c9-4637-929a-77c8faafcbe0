import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';

class OrderStatusBadge extends StatelessWidget {
  final int? statusId;

  const OrderStatusBadge({
    super.key,
    required this.statusId,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: AlignmentDirectional.bottomEnd,
      child: Container(
        height: AppSize.height(31),
        width: AppSize.width(77),
        decoration: BoxDecoration(
          color: _getStatusColor(),
          borderRadius: BorderRadiusDirectional.only(
            topStart: Radius.circular(10),
            bottomEnd: Radius.circular(10),
          ),
        ),
        child: Center(
          child: Text(
            _getStatusText(),
            style: AppTextStyle.darkGray12800,
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (statusId) {
      case 1:
        return AppColors.babyBlue;
      case 2:
        return AppColors.lightYellow;
      case 3:
        return AppColors.lightGreen;
      case 4:
        return AppColors.lightPink;
      case 5:
        return AppColors.grey2;
      default:
        return AppColors.primaryColor;
    }
  }

  String _getStatusText() {
    switch (statusId) {
      case 1:
        return 'new'.tr;
      case 2:
        return 'inProgress'.tr;
      case 3:
        return 'completed'.tr;
      case 4:
        return 'pending'.tr;
      case 5:
        return 'cancel'.tr;
      default:
        return '';
    }
  }
}
