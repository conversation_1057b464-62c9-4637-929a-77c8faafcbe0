import 'package:get/get.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/settings_controller.dart';

import '../../../../../core/services/cash_data_source.dart';
import '../../../../../injection_controller.dart';
import '../../../../auth/login/presentation/getx/controllers/login_controller.dart';
import '../../../../lock_screen/controllers/open_lock_sceen_controller.dart';
import '../controllers/printers_ip_address_controller.dart';
import '../controllers/tables_activation_controller.dart';

class SettingsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => CashDataSource());
    Get.lazyPut<PrintersIPAddressController>(
      () => PrintersIPAddressController(),
    );
    Get.lazyPut<SettingsController>(
      () => SettingsController(),
    );
    Get.lazyPut<TablesActivationController>(
      () => TablesActivationController(),
    );
    Get.lazyPut<LoginController>(
      () => LoginController(
        InjectionController().getIt(),
      ),
    );
    Get.lazyPut<OpenLockScreenController>(
      () => OpenLockScreenController(),
    );
  }
}
