import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/lock_screen/controllers/open_lock_sceen_controller.dart';

import '../../../core/errors/exceptions.dart';
import '../../../core/errors/failure.dart';
import '../../../core/widgets/failed_snack_bar.dart';
import '../../auth/login/data/models/login_model.dart';
import '../../auth/login/domain/entitis/login_intity.dart';
import '../../auth/login/domain/use_cases/login_use_case.dart';

class LockScreenController extends GetxController {
  final OpenLockScreenController openLockScreenController =
      Get.find<OpenLockScreenController>();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final LoginUseCase loginUseCase;
  final loading = false.obs;
  final TextEditingController otpController = TextEditingController();
  LoginTaskModel loginTaskModel = LoginTaskModel();
  LockScreenController(this.loginUseCase);
  Future<void> login() async {
    if (formKey.currentState!.validate()) {
      String otp = otpController.text;
      final result = await loginUseCase.call(LoginParam(
        loading: loading,
        pinCode: otp,
      ));
      result.fold(
        (failure) {
          String errorMessage;
          if (failure is ServerFailure) {
            errorMessage = failure.message;
          } else if (failure is AppExceptions) {
            errorMessage = failure.message;
          } else {
            errorMessage = 'somethingWentWrongPleaseTryAgainLater'.tr;
          }

          failedSnaskBar(errorMessage);
        },
        (data) {
          loginTaskModel = data;
          Get.back();
          openLockScreenController.initializeTimer();
        },
      );
    }
  }
}
