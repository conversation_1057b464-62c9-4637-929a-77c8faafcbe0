import 'package:get/get.dart';
import 'package:point_of_sale/features/payment/presentation/getx/controllers/pay_order_controller.dart';
import 'package:point_of_sale/features/payment/presentation/getx/controllers/payment_methods_controller.dart';

import '../../../../../core/widgets/failed_snack_bar.dart';
import 'payment_calculation_controller.dart';

class PaymentMethodController extends GetxController {
  final PaymentMethodsController paymentMethodsController =
      Get.find<PaymentMethodsController>();
  final PayOrderController payOrderController = Get.find<PayOrderController>();
  final PaymentCalculationController calculationController =
      Get.find<PaymentCalculationController>();

  // Selected payment method
  var selectedMethodIndex = (-1).obs;
  var methodPaidAmounts = <double>[].obs;

  // Getters
  int get selectedIndex => selectedMethodIndex.value;
  List<double> get paidAmounts => methodPaidAmounts;

  void selectMethod(int index) {
    if (methodPaidAmounts.isEmpty ||
        paymentMethodsController.paymentMethodsModel.isNotEmpty) {
      methodPaidAmounts.value = List<double>.filled(
        paymentMethodsController.paymentMethodsModel.length,
        0.0,
      );
    }

    if (selectedMethodIndex.value >= 0) {
      final updatedAmounts = List<double>.from(methodPaidAmounts);
      updatedAmounts[selectedMethodIndex.value] =
          double.tryParse(payOrderController.money.value) ?? 0.0;
      methodPaidAmounts.value = updatedAmounts;
    }

    selectedMethodIndex.value = index;
    payOrderController.money.value =
        methodPaidAmounts[index].toStringAsFixed(2);
    update();
  }

  void onNumberPressed(String digit) {
    if (selectedMethodIndex.value < 0) {
      failedSnaskBar('Please select a payment method first');
      return;
    }

    if (payOrderController.money.value == "0" ||
        payOrderController.money.value == "0.00") {
      payOrderController.money.value = digit;
    } else {
      payOrderController.money.value += digit;
    }
    updateMethodPaidAmount();
    update();
  }

  void onClearPressed() {
    if (selectedMethodIndex.value < 0) {
      return;
    }

    if (payOrderController.money.value.isNotEmpty) {
      payOrderController.money.value = payOrderController.money.value
          .substring(0, payOrderController.money.value.length - 1);
      if (payOrderController.money.value.isEmpty) {
        payOrderController.money.value = "0";
      }
      updateMethodPaidAmount();
      update();
    }
  }

  void onAddValue(double value) {
    if (selectedMethodIndex.value < 0) {
      failedSnaskBar('Please select a payment method first');
      return;
    }

    final current = double.tryParse(payOrderController.money.value) ?? 0.0;
    final newVal = current + value;
    payOrderController.money.value = newVal.toStringAsFixed(2);
    updateMethodPaidAmount();
    update();
  }

  void updateMethodPaidAmount() {
    if (selectedMethodIndex.value < 0) {
      return;
    }

    if (selectedMethodIndex.value < methodPaidAmounts.length) {
      final updatedAmounts = List<double>.from(methodPaidAmounts);
      updatedAmounts[selectedMethodIndex.value] =
          double.tryParse(payOrderController.money.value) ?? 0.0;
      methodPaidAmounts.value = updatedAmounts;
      methodPaidAmounts.refresh();

      // Update calculation controller
      calculationController.updateMethodPaidAmounts(methodPaidAmounts);
    }
  }

  void initializePaymentMethods() {
    if (paymentMethodsController.paymentMethodsModel.isNotEmpty) {
      methodPaidAmounts.value = List<double>.filled(
          paymentMethodsController.paymentMethodsModel.length, 0.0);
      calculationController.updateMethodPaidAmounts(methodPaidAmounts);
    } else {
      Future.delayed(const Duration(milliseconds: 200), () {
        if (paymentMethodsController.paymentMethodsModel.isNotEmpty) {
          methodPaidAmounts.value = List<double>.filled(
              paymentMethodsController.paymentMethodsModel.length, 0.0);
          calculationController.updateMethodPaidAmounts(methodPaidAmounts);
        }
      });
    }
  }

  @override
  void onInit() {
    ever(payOrderController.money, (_) {
      updateMethodPaidAmount();
      update();
    });

    // Listen for changes in payment methods
    ever(paymentMethodsController.paymentMethodsModel, (_) {
      if (paymentMethodsController.paymentMethodsModel.isNotEmpty) {
        methodPaidAmounts.value = List<double>.filled(
            paymentMethodsController.paymentMethodsModel.length, 0.0);
        calculationController.updateMethodPaidAmounts(methodPaidAmounts);
        update();
      }
    });

    super.onInit();
  }
}
