import 'package:get/get.dart';
import 'package:point_of_sale/features/home/<USER>/models/close_session_info_model.dart';
import 'package:point_of_sale/features/home/<USER>/use_case/close_session_info_use_case.dart';

import '../../../../../core/errors/exceptions.dart';
import '../../../../../core/errors/failure.dart';
import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/services/session_manager.dart';
import '../../../../../core/widgets/failed_snack_bar.dart';
import '../../../domain/entitis/close_session_info_entity.dart';

class CloseSessionInfoController extends GetxController {
  CloseSessionInfoModel closeSessionInfoModel = CloseSessionInfoModel();
  final CashDataSource cashDataSource = Get.put(CashDataSource());
  final CloseSessionInfoUseCase closeSessionInfoUseCase;

  CloseSessionInfoController(this.closeSessionInfoUseCase);

  final loading = false.obs;
  void closeSessionInfo() async {
    // Debug: Check what's in cache
    final rawSessionId = cashDataSource.box.read('session_id');
    Get.log(
        'CloseSessionInfoController: Raw session ID from cache: $rawSessionId (type: ${rawSessionId.runtimeType})');

    // Get session ID from cache with proper type handling
    int sessionId = 0;

    try {
      if (SessionManager.currentSessionId != null) {
        sessionId = SessionManager.currentSessionId!;
        Get.log(
            'CloseSessionInfoController: Using SessionManager session ID: $sessionId');
      } else if (rawSessionId != null) {
        if (rawSessionId is int) {
          sessionId = rawSessionId;
        } else if (rawSessionId is String) {
          sessionId = int.tryParse(rawSessionId) ?? 0;
        }
        Get.log('CloseSessionInfoController: Converted session ID: $sessionId');
      }
    } catch (e) {
      Get.log('CloseSessionInfoController: Error getting session ID: $e');
    }

    Get.log('CloseSessionInfoController: Final session ID: $sessionId');

    if (sessionId == 0) {
      failedSnaskBar('noActiveSessionFound'.tr);
      return;
    }

    final result = await closeSessionInfoUseCase(
      CloseSessionInfoEntity(
        loading: loading,
        sessionId: sessionId,
      ),
    );
    result.fold(
      (failure) {
        String errorMessage;
        if (failure is ServerFailure) {
          errorMessage = failure.message;
        } else if (failure is AppExceptions) {
          errorMessage = failure.message;
        } else {
          errorMessage = 'somethingWentWrongPleaseTryAgainLater'.tr;
        }

        failedSnaskBar(errorMessage);
      },
      (data) {
        closeSessionInfoModel = data;
      },
    );
  }

  @override
  onClose() {
    super.onClose();
    closeSessionInfo();
  }
}
