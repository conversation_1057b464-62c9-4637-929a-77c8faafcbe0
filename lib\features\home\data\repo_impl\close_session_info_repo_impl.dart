import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import 'package:point_of_sale/core/errors/failure.dart';

import 'package:point_of_sale/features/home/<USER>/models/close_session_info_model.dart';

import 'package:point_of_sale/features/home/<USER>/entitis/close_session_info_entity.dart';

import '../../../../core/errors/exceptions.dart';
import '../../domain/repo/close_session_info_repo.dart';
import '../remote/close_session_info_remote_data_source.dart';

class CloseSessionInfoRepoImpl implements CloseSessionInfoRepo {
  final CloseSessionInfoRemoteDataSource closeSessionInfoRemoteDataSource;
  CloseSessionInfoRepoImpl(this.closeSessionInfoRemoteDataSource);

  @override
  Future<Either<Failure, CloseSessionInfoModel>> closeSessionInfo(
      CloseSessionInfoEntity closeSessionInfoEntity) async {
    try {
      final result = await closeSessionInfoRemoteDataSource
          .closeSessionInfo(closeSessionInfoEntity);
      return Right(result);
    } on AppExceptions catch (e) {
      return Left(ServerFailure(e.message));
    } on DioException catch (e) {
      return Left(ServerFailure(e.message ?? 'Network error occurred'));
    } catch (e) {
      return Left(
        ServerFailure(
          'Unexpected error: ${e.toString()}',
        ),
      );
    }
  }
}
