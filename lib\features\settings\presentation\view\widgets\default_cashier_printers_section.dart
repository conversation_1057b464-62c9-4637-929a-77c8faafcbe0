import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/core/utils/size_config.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';

class DefaultCashierPrintersSection extends StatelessWidget {
  const DefaultCashierPrintersSection({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PrintersIPAddressController>();

    return Obx(() {
      final availablePrinters = controller.getAvailableCashierPrinters();

      if (availablePrinters.isEmpty) {
        return Container(
          padding: EdgeInsets.all(AppSize.height(16)),
          margin: EdgeInsets.symmetric(vertical: AppSize.height(8)),
          decoration: BoxDecoration(
            color: AppColors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.grey.withValues(alpha: 0.3)),
          ),
          child: Column(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.grey,
                size: AppSize.height(32),
              ),
              SizedBox(height: AppSize.height(8)),
              Text(
                'noPrintersAvailableForCashierRegister'.tr,
                style: AppTextStyle.primary14700.copyWith(
                  color: AppColors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppSize.height(4)),
              Text(
                'pleaseConfigurePrintersFirst'.tr,
                style: AppTextStyle.primary12700.copyWith(
                  color: AppColors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.point_of_sale_outlined,
                color: AppColors.primaryColor,
                size: AppSize.height(20),
              ),
              SizedBox(width: AppSize.width(8)),
              Text(
                'defaultCashierRegisterPrinters'.tr,
                style: AppTextStyle.primary16800.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: AppSize.height(8)),
          Text(
            'selectPrintersForCashierRegister'.tr,
            style: AppTextStyle.primary12700.copyWith(
              color: AppColors.grey,
            ),
          ),
          SizedBox(height: AppSize.height(12)),

          // List of available printers with checkboxes
          ...availablePrinters.map((printer) {
            final isSelected = controller.isDefaultCashierPrinter(printer.ip);

            return Container(
              margin: EdgeInsets.only(bottom: AppSize.height(8)),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primaryColor.withValues(alpha: 0.1)
                    : AppColors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected
                      ? AppColors.primaryColor
                      : AppColors.lavenderGray,
                ),
              ),
              child: CheckboxListTile(
                value: isSelected,
                onChanged: (bool? value) {
                  controller.toggleDefaultCashierPrinter(printer.ip);
                },
                title: Row(
                  children: [
                    Icon(
                      Icons.print_outlined,
                      color:
                          isSelected ? AppColors.primaryColor : AppColors.grey,
                      size: AppSize.height(20),
                    ),
                    SizedBox(width: AppSize.width(8)),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            printer.type.translatedDisplayName,
                            style: AppTextStyle.primary14700.copyWith(
                              color: isSelected
                                  ? AppColors.primaryColor
                                  : AppColors.black,
                            ),
                          ),
                          Text(
                            printer.ip,
                            style: AppTextStyle.primary12700.copyWith(
                              color: AppColors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                activeColor: AppColors.primaryColor,
                checkColor: AppColors.white,
                controlAffinity: ListTileControlAffinity.trailing,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: AppSize.width(12),
                  vertical: AppSize.height(4),
                ),
              ),
            );
          }),

          // Action buttons
          if (controller.defaultCashierPrinters.isNotEmpty) ...[
            SizedBox(height: AppSize.height(12)),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: controller.clearDefaultCashierPrinters,
                    icon: Icon(
                      Icons.clear_all,
                      size: AppSize.height(16),
                    ),
                    label: Text('clearAll'.tr),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.red,
                      side: BorderSide(color: AppColors.red),
                      padding: EdgeInsets.symmetric(
                        vertical: AppSize.height(8),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: AppSize.width(12)),
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSize.width(12),
                      vertical: AppSize.height(8),
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: AppColors.primaryColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: AppColors.primaryColor,
                          size: AppSize.height(16),
                        ),
                        SizedBox(width: AppSize.width(4)),
                        Text(
                          '${controller.defaultCashierPrinters.length} ${'selected'.tr}',
                          style: AppTextStyle.primary12700.copyWith(
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      );
    });
  }
}
